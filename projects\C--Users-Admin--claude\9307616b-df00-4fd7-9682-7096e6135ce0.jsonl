{"type":"summary","summary":"MCP Settings Reset and Cleared","leafUuid":"2e0be377-f5d7-43e2-a372-4addd13e3be7"}
{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"ed1bb4ea-89e0-4a79-bc64-b90707554c23"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\.claude","sessionId":"9307616b-df00-4fd7-9682-7096e6135ce0","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."},"isMeta":true,"uuid":"4db7bec0-9536-4f42-ae3a-882218194a7a","timestamp":"2025-09-01T06:44:20.809Z"}
{"parentUuid":"4db7bec0-9536-4f42-ae3a-882218194a7a","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\.claude","sessionId":"9307616b-df00-4fd7-9682-7096e6135ce0","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"255be60e-8753-465b-b223-19d4943ac5e1","timestamp":"2025-09-01T06:44:47.161Z"}
{"parentUuid":"255be60e-8753-465b-b223-19d4943ac5e1","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\.claude","sessionId":"9307616b-df00-4fd7-9682-7096e6135ce0","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<local-command-stdout>(no content)</local-command-stdout>"},"uuid":"28357ea4-1fe9-4d26-8008-c032c74d2edb","timestamp":"2025-09-01T06:44:47.161Z"}
