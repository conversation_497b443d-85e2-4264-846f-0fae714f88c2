{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"0cc1d070-bca2-414e-ad74-abc000d66075","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"edf5f785-8488-48c8-9135-a9223809540f","timestamp":"2025-09-01T04:21:59.514Z"}
{"parentUuid":"edf5f785-8488-48c8-9135-a9223809540f","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>