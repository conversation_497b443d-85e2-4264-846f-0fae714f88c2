{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"ce47aaac-74b4-42fc-80cf-3fd447a6f0b4"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"193a68d0-2eb5-41ba-a006-1ff9d184cfd6","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"4fb660ed-295e-468b-b22c-236dc1fb6614","timestamp":"2025-09-01T01:39:04.660Z"}
{"parentUuid":"4fb660ed-295e-468b-b22c-236dc1fb6614","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>