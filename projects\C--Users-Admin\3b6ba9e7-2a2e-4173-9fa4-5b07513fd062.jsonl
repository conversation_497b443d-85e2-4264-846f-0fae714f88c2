{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"74d88df9-7e9c-42d1-b914-954dcaa128cf"}
{"type":"summary","summary":"Local Command Execution: Doctor Command Attempted","leafUuid":"2329760b-340b-4fe6-8552-6382ee899895"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"3b6ba9e7-2a2e-4173-9fa4-5b07513fd062","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"7b31326b-75b4-4c2a-b78f-0733ce88eec2","timestamp":"2025-09-01T07:19:16.507Z"}
{"parentUuid":"7b31326b-75b4-4c2a-b78f-0733ce88eec2","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>