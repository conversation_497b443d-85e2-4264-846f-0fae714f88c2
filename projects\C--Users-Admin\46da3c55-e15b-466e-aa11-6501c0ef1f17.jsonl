{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"cf534c47-1616-491b-b118-df9761abd1dc"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"46da3c55-e15b-466e-aa11-6501c0ef1f17","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"c3a498db-8111-4b61-91da-8a750fc0791a","timestamp":"2025-09-01T04:24:13.906Z"}
{"parentUuid":"c3a498db-8111-4b61-91da-8a750fc0791a","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>