{"type":"summary","summary":"Local Command Execution: Doctor Command Attempted","leafUuid":"4d3e3920-e71d-4fa6-90c4-70aeed346e8b"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"6a12d017-12ce-4bc9-87f9-008afd840bc1","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"d7d287a8-e93d-4789-840b-cf8e88b62d76","timestamp":"2025-09-01T07:47:00.744Z"}
{"parentUuid":"d7d287a8-e93d-4789-840b-cf8e88b62d76","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>