{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"7cf590fa-4704-4f6e-b2c4-34a1ac265ac6","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n          <command-message>mcp</command-message>\n          <command-args></command-args>"},"uuid":"f6712e6f-2c9b-4505-a777-0300ed4ca587","timestamp":"2025-08-29T10:07:15.298Z"}
{"parentUuid":"f6712e6f-2c9b-4505-a777-0300ed4ca587","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"7cf590fa-4704-4f6e-b2c4-34a1ac265ac6","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."},"isMeta":true,"uuid":"69123d0b-623e-40ce-85cb-fe706d4889cf","timestamp":"2025-08-29T10:07:18.169Z"}
{"parentUuid":"69123d0b-623e-40ce-85cb-fe706d4889cf","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\n          <command-message>exit</command-message>\n          <command-args></command-args>"},"uuid":"5c340910-4bbf-4f6a-84e7-3779d6a959cc","timestamp":"2025-08-29T10:07:18.155Z"}
{"parentUuid":"5c340910-4bbf-4f6a-84e7-3779d6a959cc","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>