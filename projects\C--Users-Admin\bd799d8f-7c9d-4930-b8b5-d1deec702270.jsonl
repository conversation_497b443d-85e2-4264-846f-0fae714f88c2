{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"bd799d8f-7c9d-4930-b8b5-d1deec702270","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"a0fa99e9-7bbf-4086-a1dd-b6a417a48241","timestamp":"2025-09-01T06:16:38.115Z"}
{"parentUuid":"a0fa99e9-7bbf-4086-a1dd-b6a417a48241","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>