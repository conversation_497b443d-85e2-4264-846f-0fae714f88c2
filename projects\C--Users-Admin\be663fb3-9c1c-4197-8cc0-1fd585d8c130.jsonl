{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"08ced430-d1ed-4f53-a25d-431dcb21ffda"}
{"type":"summary","summary":"Local Command Execution: Doctor Command Attempted","leafUuid":"08738845-a9af-4be5-82e7-28862ba2b1f6"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"be663fb3-9c1c-4197-8cc0-1fd585d8c130","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"94350204-c1e4-4e76-8fdc-c890e8612b97","timestamp":"2025-09-01T07:17:06.751Z"}
{"parentUuid":"94350204-c1e4-4e76-8fdc-c890e8612b97","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>