{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"b805b596-217c-4613-b41d-85108f2eae0f"}
{"type":"summary","summary":"MCP Server Connection Debugging in Claude Code","leafUuid":"9c0d92b1-a524-4f79-a4f2-ec4f554a3dc7"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"cc0ff11d-833c-47c3-aa3d-e7d04e72f774","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"47218f88-647a-464a-915c-6dd79abfc325","timestamp":"2025-09-01T06:12:20.660Z"}
{"parentUuid":"47218f88-647a-464a-915c-6dd79abfc325","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>\\Users\\Admin","sessionId":"cc0ff11d-833c-47c3-aa3d-e7d04e72f774","version":"1.0.61","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"5ed59dc9-11c9-4f43-b46e-0b4bc8c1aa57","timestamp":"2025-09-01T06:12:24.647Z"}
{"parentUuid":"5ed59dc9-11c9-4f43-b46e-0b4bc8c1aa57","isSidechain":false,"userType":"external","cwd":"C:\\Users\\<USER>