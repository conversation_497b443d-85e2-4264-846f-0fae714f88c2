{"type":"summary","summary":"Chrome-Style Keyboard Shortcuts for WindTerm","leafUuid":"************************************"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"现在ctrl+enter被占用了?"},"uuid":"5f7cef61-abed-48c0-8af2-3413e05af8ab","timestamp":"2025-08-29T03:19:20.593Z"}
{"parentUuid":"5f7cef61-abed-48c0-8af2-3413e05af8ab","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"text","text":"[Request interrupted by user]"}]},"uuid":"fd4d88ad-eaad-4e0d-913d-39e875973a03","timestamp":"2025-08-29T03:19:23.267Z"}
{"parentUuid":"fd4d88ad-eaad-4e0d-913d-39e875973a03","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"现在ctrl+enter被占用了?"},"uuid":"011bf748-9b9b-4767-8069-953846ea147c","timestamp":"2025-08-29T03:19:25.570Z"}
{"parentUuid":"011bf748-9b9b-4767-8069-953846ea147c","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01Fqu8jgT9hkWJpbxhJHx2vK","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"我来帮你检查 WindTerm 中 Ctrl+Enter 快捷键的配置情况。让我查看键盘映射文件来了解当前的绑定。"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":1935,"cache_read_input_tokens":14242,"cache_creation":{"ephemeral_5m_input_tokens":1935,"ephemeral_1h_input_tokens":0},"output_tokens":3,"service_tier":"standard"}},"requestId":"req_011CSbH9pppWnYbVNfoqdg9N","type":"assistant","uuid":"84a46681-5da6-4118-ad53-3d2265f10d45","timestamp":"2025-08-29T03:19:30.575Z"}
{"parentUuid":"84a46681-5da6-4118-ad53-3d2265f10d45","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01Fqu8jgT9hkWJpbxhJHx2vK","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01FeMnpXW2ECc8ppz6LBiKAS","name":"Grep","input":{"pattern":"ctrl.*enter|Ctrl.*Enter","glob":"*.keymaps","output_mode":"content","-i":true}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":1935,"cache_read_input_tokens":14242,"cache_creation":{"ephemeral_5m_input_tokens":1935,"ephemeral_1h_input_tokens":0},"output_tokens":168,"service_tier":"standard"}},"requestId":"req_011CSbH9pppWnYbVNfoqdg9N","type":"assistant","uuid":"2aea499f-9eba-4706-9b5e-053754be17e8","timestamp":"2025-08-29T03:19:31.647Z"}
{"parentUuid":"2aea499f-9eba-4706-9b5e-053754be17e8","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01FeMnpXW2ECc8ppz6LBiKAS","type":"tool_result","content":"E:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\","}]},"uuid":"d28a8d6e-c249-46f1-90e6-e107c3f73a48","timestamp":"2025-08-29T03:19:31.722Z","toolUseResult":{"mode":"content","numFiles":0,"filenames":[],"content":"E:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind-bak.keymaps:  { \"keys\": \"<Ctrl+Enter>\",","numLines":8}}
{"parentUuid":"d28a8d6e-c249-46f1-90e6-e107c3f73a48","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_018NQjfD1tZTXdC1DJrEEUeh","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"让我查看具体的 Ctrl+Enter 绑定详情："}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":5,"cache_creation_input_tokens":609,"cache_read_input_tokens":16177,"cache_creation":{"ephemeral_5m_input_tokens":609,"ephemeral_1h_input_tokens":0},"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CSbHAHXRU6okLaBuUBai5","type":"assistant","uuid":"645fdb72-0d7d-4ba0-9c8e-4a6510c1edbe","timestamp":"2025-08-29T03:19:36.077Z"}
{"parentUuid":"645fdb72-0d7d-4ba0-9c8e-4a6510c1edbe","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_018NQjfD1tZTXdC1DJrEEUeh","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01E1h4NkpUj7xrmwDjWuWsSX","name":"Grep","input":{"pattern":"Ctrl\\+Enter.*?}.*?}","glob":"wind.keymaps","output_mode":"content","-A":3,"-B":1}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":5,"cache_creation_input_tokens":609,"cache_read_input_tokens":16177,"cache_creation":{"ephemeral_5m_input_tokens":609,"ephemeral_1h_input_tokens":0},"output_tokens":157,"service_tier":"standard"}},"requestId":"req_011CSbHAHXRU6okLaBuUBai5","type":"assistant","uuid":"d7577112-1806-4ca9-8768-52a1ab3c1c45","timestamp":"2025-08-29T03:19:37.481Z"}
{"parentUuid":"d7577112-1806-4ca9-8768-52a1ab3c1c45","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01E1h4NkpUj7xrmwDjWuWsSX","type":"tool_result","content":"No matches found"}]},"uuid":"a5afaa2b-3ba1-438c-b951-a83f6c628eb9","timestamp":"2025-08-29T03:19:37.558Z","toolUseResult":{"mode":"content","numFiles":0,"filenames":[],"content":"","numLines":0}}
{"parentUuid":"a5afaa2b-3ba1-438c-b951-a83f6c628eb9","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01EFkkEzRYwRDDtapwpx3n84","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"让我查看更详细的上下文："}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":4,"cache_creation_input_tokens":329,"cache_read_input_tokens":16786,"cache_creation":{"ephemeral_5m_input_tokens":329,"ephemeral_1h_input_tokens":0},"output_tokens":4,"service_tier":"standard"}},"requestId":"req_011CSbHAhkhUFHXsVmY57MA9","type":"assistant","uuid":"c7d7fed0-bca5-4253-9bbe-e25cae73bf9f","timestamp":"2025-08-29T03:19:41.292Z"}
{"parentUuid":"c7d7fed0-bca5-4253-9bbe-e25cae73bf9f","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01EFkkEzRYwRDDtapwpx3n84","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01LK9tWserndHWNvwiZLXFJF","name":"Grep","input":{"pattern":"<Ctrl\\+Enter>","glob":"wind.keymaps","output_mode":"content","-A":5,"-B":2}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":4,"cache_creation_input_tokens":329,"cache_read_input_tokens":16786,"cache_creation":{"ephemeral_5m_input_tokens":329,"ephemeral_1h_input_tokens":0},"output_tokens":145,"service_tier":"standard"}},"requestId":"req_011CSbHAhkhUFHXsVmY57MA9","type":"assistant","uuid":"ca2430f5-280f-4cc5-a8da-f8f011306141","timestamp":"2025-08-29T03:19:42.177Z"}
{"parentUuid":"ca2430f5-280f-4cc5-a8da-f8f011306141","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01LK9tWserndHWNvwiZLXFJF","type":"tool_result","content":"E:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Text.MoveToDocumentEnd\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"normal\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetCommandMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"command\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetNormalMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"local\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetRemoteMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"remote\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetLocalMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  { \"keys\": \"<Ctrl+F>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"normal, local, widget\","}]},"uuid":"13075289-e23f-44c0-8226-43926cb6ed93","timestamp":"2025-08-29T03:19:42.237Z","toolUseResult":{"mode":"content","numFiles":0,"filenames":[],"content":"E:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Text.MoveToDocumentEnd\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"normal\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetCommandMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"command\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetNormalMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"local\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetRemoteMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps:  { \"keys\": \"<Ctrl+Enter>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"remote\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"action\": \"Window.SetLocalMode\"\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-  { \"keys\": \"<Ctrl+F>\",\r\nE:\\WindTerm_2.7.0\\global\\wind.keymaps-    \"modes\": \"normal, local, widget\",","numLines":20}}
{"parentUuid":"13075289-e23f-44c0-8226-43926cb6ed93","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01JUgg8h91LshhwZwEJz2Ldp","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"现在我明白了！在 WindTerm 中，Ctrl+Enter 确实被占用了，它有以下几个功能：\n\n1. **normal 模式**: `Window.SetCommandMode` - 切换到命令模式\n2. **command 模式**: `Window.SetNormalMode` - 切换回普通模式  \n3. **local 模式**: `Window.SetRemoteMode` - 切换到远程模式\n4. **remote 模式**: `Window.SetLocalMode` - 切换到本地模式\n\n这个快捷键主要用于在不同的操作模式之间切换。如果你想要释放或修改这个快捷键绑定，我可以帮你修改键盘映射配置。"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":902,"cache_read_input_tokens":17115,"cache_creation":{"ephemeral_5m_input_tokens":902,"ephemeral_1h_input_tokens":0},"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CSbHB3c3bJmFK5qvDdHWM","type":"assistant","uuid":"2f945f91-e2c2-4486-91aa-2e7a0d638244","timestamp":"2025-08-29T03:19:49.846Z"}
{"parentUuid":"2f945f91-e2c2-4486-91aa-2e7a0d638244","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01JUgg8h91LshhwZwEJz2Ldp","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_011kkNEngrA49uikco16FyWz","name":"ExitPlanMode","input":{"plan":"## 修改 Ctrl+Enter 快捷键绑定的计划\n\n**当前状态分析:**\n- Ctrl+Enter 在 WindTerm 中被用于4种不同模式的切换\n- normal ↔ command 模式切换\n- local ↔ remote 模式切换\n\n**可选方案:**\n1. **完全删除** Ctrl+Enter 绑定，释放该快捷键\n2. **修改绑定** 将这些功能改为其他快捷键组合\n3. **保留部分** 只保留你需要的模式切换功能\n\n**实施步骤:**\n1. 备份当前的 `wind.keymaps` 文件\n2. 根据你的选择修改相应的快捷键绑定\n3. 重启 WindTerm 使更改生效\n\n请告诉我你希望如何处理这个快捷键冲突。"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":902,"cache_read_input_tokens":17115,"cache_creation":{"ephemeral_5m_input_tokens":902,"ephemeral_1h_input_tokens":0},"output_tokens":505,"service_tier":"standard"}},"requestId":"req_011CSbHB3c3bJmFK5qvDdHWM","type":"assistant","uuid":"a9d419c2-d04c-429a-b383-ce151f115e4e","timestamp":"2025-08-29T03:19:55.771Z"}
{"parentUuid":"a9d419c2-d04c-429a-b383-ce151f115e4e","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"tool_result","content":"The user doesn't want to proceed with this tool use. The tool use was rejected (eg. if it was a file edit, the new_string was NOT written to the file). STOP what you are doing and wait for the user to tell you how to proceed.","is_error":true,"tool_use_id":"toolu_011kkNEngrA49uikco16FyWz"}]},"uuid":"04443a6b-6da3-4c08-89cd-bb9b6cd9711a","timestamp":"2025-08-29T03:20:25.154Z","toolUseResult":"Error: The user doesn't want to proceed with this tool use. The tool use was rejected (eg. if it was a file edit, the new_string was NOT written to the file). STOP what you are doing and wait for the user to tell you how to proceed."}
{"parentUuid":"04443a6b-6da3-4c08-89cd-bb9b6cd9711a","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"text","text":"[Request interrupted by user for tool use]"}]},"uuid":"c1df88dc-d956-40a9-be80-679bcd7e41fd","timestamp":"2025-08-29T03:20:25.303Z"}
{"parentUuid":"c1df88dc-d956-40a9-be80-679bcd7e41fd","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"他目前的快捷键作用是什么?"},"uuid":"b64b8707-bd22-4dd3-adb3-6d751a54c674","timestamp":"2025-08-29T03:20:33.264Z"}
{"parentUuid":"b64b8707-bd22-4dd3-adb3-6d751a54c674","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01NKpzW23dz86KrPnzHP17Q2","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"根据我刚才查看的配置，Ctrl+Enter 在 WindTerm 中目前的作用是：\n\n1. **在 normal 模式下**: 切换到 command 模式 (`Window.SetCommandMode`)\n2. **在 command 模式下**: 切换回 normal 模式 (`Window.SetNormalMode`)\n3. **在 local 模式下**: 切换到 remote 模式 (`Window.SetRemoteMode`)\n4. **在 remote 模式下**: 切换到 local 模式 (`Window.SetLocalMode`)\n\n简单来说，Ctrl+Enter 是用于在 WindTerm 的不同操作模式之间快速切换的快捷键。这是 WindTerm 的模式切换功能，类似于一些编辑器的模式概念。"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":860,"cache_read_input_tokens":18017,"cache_creation":{"ephemeral_5m_input_tokens":860,"ephemeral_1h_input_tokens":0},"output_tokens":222,"service_tier":"standard"}},"requestId":"req_011CSbHEqHVaHKh3HLAYuxhC","type":"assistant","uuid":"f59966d0-c1eb-43a3-91be-d4c59b6b8138","timestamp":"2025-08-29T03:20:40.845Z"}
{"parentUuid":"f59966d0-c1eb-43a3-91be-d4c59b6b8138","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"详细一点说说他的作用"},"uuid":"9ca464dc-9b75-42ac-8140-e75413757302","timestamp":"2025-08-29T03:21:00.549Z"}
{"parentUuid":"9ca464dc-9b75-42ac-8140-e75413757302","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_019pRQWjohUoCGHCWCMQmPDk","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"让我更详细地解释 WindTerm 中 Ctrl+Enter 的具体作用："}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":391,"cache_read_input_tokens":18877,"cache_creation":{"ephemeral_5m_input_tokens":391,"ephemeral_1h_input_tokens":0},"output_tokens":2,"service_tier":"standard"}},"requestId":"req_011CSbHGsXz8iTYfhNtsR9Bz","type":"assistant","uuid":"bc9c918b-2431-4ea4-84bf-3e4a129077a6","timestamp":"2025-08-29T03:21:05.826Z"}
{"parentUuid":"bc9c918b-2431-4ea4-84bf-3e4a129077a6","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_019pRQWjohUoCGHCWCMQmPDk","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01JHj2VmYbPyku8zdR2hpaj8","name":"Grep","input":{"pattern":"Window\\.SetCommandMode|Window\\.SetNormalMode|Window\\.SetRemoteMode|Window\\.SetLocalMode","glob":"wind.actions","output_mode":"content","-A":5,"-B":2}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":391,"cache_read_input_tokens":18877,"cache_creation":{"ephemeral_5m_input_tokens":391,"ephemeral_1h_input_tokens":0},"output_tokens":178,"service_tier":"standard"}},"requestId":"req_011CSbHGsXz8iTYfhNtsR9Bz","type":"assistant","uuid":"c15080c0-c190-4091-bf15-d78c208e2a31","timestamp":"2025-08-29T03:21:06.983Z"}
{"parentUuid":"c15080c0-c190-4091-bf15-d78c208e2a31","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01JHj2VmYbPyku8zdR2hpaj8","type":"tool_result","content":"E:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Repeat Last Command\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetCommandMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Command Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.CommandMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.CommandMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetLocalMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Local Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.LocalMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.LocalMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetNormalMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Normal Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.NormalMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.NormalMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetRemoteMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Remote Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.RemoteMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.RemoteMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  \"Window.SetTabColor\" : {"}]},"uuid":"856e9aaf-ff26-4e29-b5de-4361fe465110","timestamp":"2025-08-29T03:21:07.047Z","toolUseResult":{"mode":"content","numFiles":0,"filenames":[],"content":"E:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Repeat Last Command\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetCommandMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Command Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.CommandMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.CommandMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetLocalMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Local Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.LocalMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.LocalMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetNormalMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Normal Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.NormalMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.NormalMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions:  \"Window.SetRemoteMode\" : {\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"text\"      : \"&Remote Mode\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"state\"     : \"(action) => { action.checked = action.checkable = (window.activeText.keyMode == King.RemoteMode) }\",\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-    \"script\"    : \"(action) => { window.activeText.keyMode = King.RemoteMode }\"\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  },\r\nE:\\WindTerm_2.7.0\\global\\wind.actions-  \"Window.SetTabColor\" : {","numLines":23}}
{"parentUuid":"856e9aaf-ff26-4e29-b5de-4361fe465110","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01JE7o6uKp9W8pUgMpmwTJEm","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"现在我可以详细解释 WindTerm 中 Ctrl+Enter 的作用了：\n\n## WindTerm 的四种键盘模式\n\nCtrl+Enter 用于在 WindTerm 的四种不同键盘模式之间切换，每种模式有不同的键盘行为：\n\n### 1. **Normal Mode (普通模式)**\n- 这是标准的终端操作模式\n- 键盘输入直接发送到终端/Shell\n- 所有字符按正常方式处理\n\n### 2. **Command Mode (命令模式)**\n- 类似于 Vi/Vim 的命令模式概念\n- 在这个模式下，键盘输入可能被解释为 WindTerm 的内部命令\n- 而不是直接发送给终端\n\n### 3. **Local Mode (本地模式)**\n- 专门用于本地文件操作和本地命令执行\n- 键盘行为针对本地系统进行优化\n\n### 4. **Remote Mode (远程模式)**\n- 专门用于远程连接（SSH、Telnet 等）\n- 键盘行为针对远程服务器操作进行优化\n\n## Ctrl+Enter 的具体切换逻辑\n\n- **当前在 Normal 模式** → 按 Ctrl+Enter → **切换到 Command 模式**\n- **当前在 Command 模式** → 按 Ctrl+Enter → **切换回 Normal 模式**\n- **当前在 Local 模式** → 按 Ctrl+Enter → **切换到 Remote 模式**\n- **当前在 Remote 模式** → 按 Ctrl+Enter → **切换到 Local 模式**\n\n这种设计让用户可以快速在不同的操作上下文之间切换，提高终端使用效率。比如在处理本地文件和远程服务器之间频繁切换时，这个功能会很有用。"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":1142,"cache_read_input_tokens":19268,"cache_creation":{"ephemeral_5m_input_tokens":1142,"ephemeral_1h_input_tokens":0},"output_tokens":537,"service_tier":"standard"}},"requestId":"req_011CSbHHNbxvxXi1BNeDcH2b","type":"assistant","uuid":"d3b0f4f5-fdec-49c9-980c-f15cc2ec4fb6","timestamp":"2025-08-29T03:21:20.423Z"}
{"parentUuid":"d3b0f4f5-fdec-49c9-980c-f15cc2ec4fb6","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"删除这个快捷键的板顶吧"},"uuid":"5250153c-803c-4472-a074-37d6cda41399","timestamp":"2025-08-29T03:21:49.586Z"}
{"parentUuid":"5250153c-803c-4472-a074-37d6cda41399","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"text","text":"[Request interrupted by user]"}]},"uuid":"3236e878-0637-4cc2-a5cd-3050720e4467","timestamp":"2025-08-29T03:21:51.577Z"}
{"parentUuid":"3236e878-0637-4cc2-a5cd-3050720e4467","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"删除这个快捷键的绑定吧"},"uuid":"56c0fcf8-d7e2-4c5a-8fd2-78ab3920a0e6","timestamp":"2025-08-29T03:21:56.993Z"}
{"parentUuid":"56c0fcf8-d7e2-4c5a-8fd2-78ab3920a0e6","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01W2DbuN8Gbe6YbsrLGWm9kM","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_016uLvVGyGJG6AtkQaMx8Y7k","name":"ExitPlanMode","input":{"plan":"## 删除 Ctrl+Enter 快捷键绑定的计划\n\n**目标**: 从 WindTerm 配置中删除所有 Ctrl+Enter 快捷键绑定\n\n**需要删除的绑定**:\n1. Normal 模式: `<Ctrl+Enter>` → `Window.SetCommandMode`\n2. Command 模式: `<Ctrl+Enter>` → `Window.SetNormalMode` \n3. Local 模式: `<Ctrl+Enter>` → `Window.SetRemoteMode`\n4. Remote 模式: `<Ctrl+Enter>` → `Window.SetLocalMode`\n\n**实施步骤**:\n1. 备份当前的 `wind.keymaps` 文件 (复制为 `wind.keymaps.backup`)\n2. 从 `wind.keymaps` 中删除所有包含 `<Ctrl+Enter>` 的键盘绑定条目\n3. 保存修改后的文件\n\n**结果**: Ctrl+Enter 快捷键将被释放，可以用于其他用途或应用程序"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":437,"cache_read_input_tokens":20950,"cache_creation":{"ephemeral_5m_input_tokens":437,"ephemeral_1h_input_tokens":0},"output_tokens":345,"service_tier":"standard"}},"requestId":"req_011CSbHM17WUzRyVGAdJkbYG","type":"assistant","uuid":"6e37cc22-ca03-45a7-827f-0886e39ba3d3","timestamp":"2025-08-29T03:22:05.175Z"}
{"parentUuid":"6e37cc22-ca03-45a7-827f-0886e39ba3d3","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"tool_result","content":"User has approved your plan. You can now start coding. Start with updating your todo list if applicable","tool_use_id":"toolu_016uLvVGyGJG6AtkQaMx8Y7k"}]},"uuid":"6590dddc-e234-4f81-a882-e775bbe5ee4e","timestamp":"2025-08-29T03:22:08.867Z","toolUseResult":{"plan":"## 删除 Ctrl+Enter 快捷键绑定的计划\n\n**目标**: 从 WindTerm 配置中删除所有 Ctrl+Enter 快捷键绑定\n\n**需要删除的绑定**:\n1. Normal 模式: `<Ctrl+Enter>` → `Window.SetCommandMode`\n2. Command 模式: `<Ctrl+Enter>` → `Window.SetNormalMode` \n3. Local 模式: `<Ctrl+Enter>` → `Window.SetRemoteMode`\n4. Remote 模式: `<Ctrl+Enter>` → `Window.SetLocalMode`\n\n**实施步骤**:\n1. 备份当前的 `wind.keymaps` 文件 (复制为 `wind.keymaps.backup`)\n2. 从 `wind.keymaps` 中删除所有包含 `<Ctrl+Enter>` 的键盘绑定条目\n3. 保存修改后的文件\n\n**结果**: Ctrl+Enter 快捷键将被释放，可以用于其他用途或应用程序","isAgent":false}}
{"parentUuid":"6590dddc-e234-4f81-a882-e775bbe5ee4e","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_011NVhKBG85xrTzAFg43ek76","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01HdpJNg8dC7yvFN7pbR7eoq","name":"TodoWrite","input":{"todos":[{"content":"备份当前的 wind.keymaps 文件","status":"pending","activeForm":"备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":375,"cache_read_input_tokens":21387,"cache_creation":{"ephemeral_5m_input_tokens":375,"ephemeral_1h_input_tokens":0},"output_tokens":216,"service_tier":"standard"}},"requestId":"req_011CSbHMsPwW7veCLAdTFKV1","type":"assistant","uuid":"52f55cf5-eb04-4cf6-91de-a0f2304acece","timestamp":"2025-08-29T03:22:14.460Z"}
{"parentUuid":"52f55cf5-eb04-4cf6-91de-a0f2304acece","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01HdpJNg8dC7yvFN7pbR7eoq","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"3c509da3-78ec-4d99-b520-ff7c8b4eb4ac","timestamp":"2025-08-29T03:22:14.490Z","toolUseResult":{"oldTodos":[],"newTodos":[{"content":"备份当前的 wind.keymaps 文件","status":"pending","activeForm":"备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}]}}
{"parentUuid":"3c509da3-78ec-4d99-b520-ff7c8b4eb4ac","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01EsVT6GW72unU9rpmogdFhP","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_019xSecwDZovNg938J6oBRZm","name":"TodoWrite","input":{"todos":[{"content":"备份当前的 wind.keymaps 文件","status":"in_progress","activeForm":"正在备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":521,"cache_read_input_tokens":21762,"cache_creation":{"ephemeral_5m_input_tokens":521,"ephemeral_1h_input_tokens":0},"output_tokens":184,"service_tier":"standard"}},"requestId":"req_011CSbHNHZjmgk4MCsGra2r2","type":"assistant","uuid":"a62736ff-9f16-413f-8c62-083db9d088f4","timestamp":"2025-08-29T03:22:19.725Z"}
{"parentUuid":"a62736ff-9f16-413f-8c62-083db9d088f4","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_019xSecwDZovNg938J6oBRZm","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"a0785ba1-bf1e-45c0-a17d-74cf348dfd2d","timestamp":"2025-08-29T03:22:19.774Z","toolUseResult":{"oldTodos":[],"newTodos":[{"content":"备份当前的 wind.keymaps 文件","status":"in_progress","activeForm":"正在备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}]}}
{"parentUuid":"a0785ba1-bf1e-45c0-a17d-74cf348dfd2d","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01JQmC5khQ9uf2wEhGPpwf8K","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_016rb5nVTnG4crqdjafWMaxq","name":"Bash","input":{"command":"copy \"wind.keymaps\" \"wind.keymaps.backup\"","description":"备份 keymaps 配置文件"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":1,"cache_creation_input_tokens":536,"cache_read_input_tokens":22283,"cache_creation":{"ephemeral_5m_input_tokens":536,"ephemeral_1h_input_tokens":0},"output_tokens":95,"service_tier":"standard"}},"requestId":"req_011CSbHNgeZ7gDPmzCFqZrJ5","type":"assistant","uuid":"ba6427c0-f945-4829-86a7-141783a87e1a","timestamp":"2025-08-29T03:22:24.682Z"}
{"parentUuid":"ba6427c0-f945-4829-86a7-141783a87e1a","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"tool_result","content":"The user doesn't want to proceed with this tool use. The tool use was rejected (eg. if it was a file edit, the new_string was NOT written to the file). STOP what you are doing and wait for the user to tell you how to proceed.","is_error":true,"tool_use_id":"toolu_016rb5nVTnG4crqdjafWMaxq"}]},"uuid":"feda22a4-68f7-4d9a-8dc4-2fad5cef65ca","timestamp":"2025-08-29T03:22:57.206Z","toolUseResult":"Error: The user doesn't want to proceed with this tool use. The tool use was rejected (eg. if it was a file edit, the new_string was NOT written to the file). STOP what you are doing and wait for the user to tell you how to proceed."}
{"parentUuid":"feda22a4-68f7-4d9a-8dc4-2fad5cef65ca","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"text","text":"[Request interrupted by user for tool use]"}]},"uuid":"3543c648-3674-4cf9-afbe-c9dd66426a4d","timestamp":"2025-08-29T03:22:57.245Z"}
{"parentUuid":"3543c648-3674-4cf9-afbe-c9dd66426a4d","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"不需要备份, 看到 @wind-bak.keymaps 了吗, 这就是我备份的, 你改你的就行"},"uuid":"9531a96c-2381-4d52-b38d-37def86d6c81","timestamp":"2025-08-29T03:23:37.521Z"}
{"parentUuid":"9531a96c-2381-4d52-b38d-37def86d6c81","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01AqQNzk3CdJcjVZYGT1ZUuQ","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01PMeV7W4swkZ4gFv5tSQLVh","name":"TodoWrite","input":{"todos":[{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"in_progress","activeForm":"正在删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":34012,"cache_read_input_tokens":22819,"cache_creation":{"ephemeral_5m_input_tokens":34012,"ephemeral_1h_input_tokens":0},"output_tokens":239,"service_tier":"standard"}},"requestId":"req_011CSbHURk2wSoRnGsC997L6","type":"assistant","uuid":"1ae10adc-4332-4a16-ac14-b450ea555ddf","timestamp":"2025-08-29T03:23:46.159Z"}
{"parentUuid":"1ae10adc-4332-4a16-ac14-b450ea555ddf","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01PMeV7W4swkZ4gFv5tSQLVh","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"b2140780-bf82-472b-9d41-7b184e0fbe91","timestamp":"2025-08-29T03:23:46.192Z","toolUseResult":{"oldTodos":[{"content":"备份当前的 wind.keymaps 文件","status":"in_progress","activeForm":"正在备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}],"newTodos":[{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"in_progress","activeForm":"正在删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}]}}
{"parentUuid":"b2140780-bf82-472b-9d41-7b184e0fbe91","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01J6xwJL5caPeg1pRJ8eJMzD","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"好的，我直接修改 wind.keymaps 文件。让我先读取文件内容："}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":404,"cache_read_input_tokens":56831,"cache_creation":{"ephemeral_5m_input_tokens":404,"ephemeral_1h_input_tokens":0},"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CSbHV5E2gjLj5W3ywknXR","type":"assistant","uuid":"22955c1f-f223-43a4-ba05-************","timestamp":"2025-08-29T03:23:51.062Z"}
{"parentUuid":"22955c1f-f223-43a4-ba05-************","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01J6xwJL5caPeg1pRJ8eJMzD","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01S2cTbA5DuzvSBoFQB8rRw5","name":"Read","input":{"file_path":"E:\\WindTerm_2.7.0\\global\\wind.keymaps","offset":1080,"limit":20}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":404,"cache_read_input_tokens":56831,"cache_creation":{"ephemeral_5m_input_tokens":404,"ephemeral_1h_input_tokens":0},"output_tokens":135,"service_tier":"standard"}},"requestId":"req_011CSbHV5E2gjLj5W3ywknXR","type":"assistant","uuid":"1fe95fe6-e030-4dc6-bdc3-b2a3be2932a3","timestamp":"2025-08-29T03:23:51.764Z"}
{"parentUuid":"1fe95fe6-e030-4dc6-bdc3-b2a3be2932a3","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01S2cTbA5DuzvSBoFQB8rRw5","type":"tool_result","content":"  1080→    \"action\": \"Window.SetNormalMode\"\n  1081→  },\n  1082→  { \"keys\": \"<Ctrl+Enter>\",\n  1083→    \"modes\": \"local\",\n  1084→    \"action\": \"Window.SetRemoteMode\"\n  1085→  },\n  1086→  { \"keys\": \"<Ctrl+Enter>\",\n  1087→    \"modes\": \"remote\",\n  1088→    \"action\": \"Window.SetLocalMode\"\n  1089→  },\n  1090→  { \"keys\": \"<Ctrl+F>\",\n  1091→    \"modes\": \"normal, local, widget\",\n  1092→    \"action\": \"Text.Find\"\n  1093→  },\n  1094→  { \"keys\": \"<Ctrl+F>\",\n  1095→    \"modes\": \"command\",\n  1096→    \"action\": \"Text.MoveToNextPage\"\n  1097→  },\n  1098→  { \"keys\": \"<Ctrl+F3>\",\n  1099→    \"modes\": \"normal, local\",\n\n<system-reminder>\nWhenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.\n</system-reminder>\n"}]},"uuid":"1ac096cf-4332-44a1-bf99-ef0d5200f4f3","timestamp":"2025-08-29T03:23:51.798Z","toolUseResult":{"type":"text","file":{"filePath":"E:\\WindTerm_2.7.0\\global\\wind.keymaps","content":"    \"action\": \"Window.SetNormalMode\"\n  },\n  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"local\",\n    \"action\": \"Window.SetRemoteMode\"\n  },\n  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"remote\",\n    \"action\": \"Window.SetLocalMode\"\n  },\n  { \"keys\": \"<Ctrl+F>\",\n    \"modes\": \"normal, local, widget\",\n    \"action\": \"Text.Find\"\n  },\n  { \"keys\": \"<Ctrl+F>\",\n    \"modes\": \"command\",\n    \"action\": \"Text.MoveToNextPage\"\n  },\n  { \"keys\": \"<Ctrl+F3>\",\n    \"modes\": \"normal, local\",","numLines":20,"startLine":1080,"totalLines":2385}}}
{"parentUuid":"1ac096cf-4332-44a1-bf99-ef0d5200f4f3","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_011Shgtww32y6L3wmaETJKEG","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"现在让我删除所有4个 Ctrl+Enter 绑定："}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":482,"cache_read_input_tokens":57235,"cache_creation":{"ephemeral_5m_input_tokens":482,"ephemeral_1h_input_tokens":0},"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CSbHVUxHhnMnYLTiFAobV","type":"assistant","uuid":"03bfb6ae-a7bf-4b3d-913c-68cee849fa39","timestamp":"2025-08-29T03:23:56.371Z"}
{"parentUuid":"03bfb6ae-a7bf-4b3d-913c-68cee849fa39","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_011Shgtww32y6L3wmaETJKEG","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01VCZdyCwfX3a9yisbLX1E6b","name":"MultiEdit","input":{"file_path":"E:\\WindTerm_2.7.0\\global\\wind.keymaps","edits":[{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"normal\",\n    \"action\": \"Window.SetCommandMode\"\n  },","new_string":""},{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"command\",\n    \"action\": \"Window.SetNormalMode\"\n  },","new_string":""},{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"local\",\n    \"action\": \"Window.SetRemoteMode\"\n  },","new_string":""},{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"remote\",\n    \"action\": \"Window.SetLocalMode\"\n  },","new_string":""}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":482,"cache_read_input_tokens":57235,"cache_creation":{"ephemeral_5m_input_tokens":482,"ephemeral_1h_input_tokens":0},"output_tokens":379,"service_tier":"standard"}},"requestId":"req_011CSbHVUxHhnMnYLTiFAobV","type":"assistant","uuid":"8e33cd6c-805a-4d42-a561-bdffcc4598a0","timestamp":"2025-08-29T03:23:59.585Z"}
{"parentUuid":"8e33cd6c-805a-4d42-a561-bdffcc4598a0","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01VCZdyCwfX3a9yisbLX1E6b","type":"tool_result","content":"Applied 4 edits to E:\\WindTerm_2.7.0\\global\\wind.keymaps:\n1. Replaced \"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"normal\",...\" with \"\"\n2. Replaced \"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"command\"...\" with \"\"\n3. Replaced \"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"local\",\n...\" with \"\"\n4. Replaced \"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"remote\",...\" with \"\""}]},"uuid":"dacb5e21-303b-434f-aa75-94357a52567a","timestamp":"2025-08-29T03:23:59.636Z","toolUseResult":{"filePath":"E:\\WindTerm_2.7.0\\global\\wind.keymaps","edits":[{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"normal\",\n    \"action\": \"Window.SetCommandMode\"\n  },","new_string":"","replace_all":false},{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"command\",\n    \"action\": \"Window.SetNormalMode\"\n  },","new_string":"","replace_all":false},{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"local\",\n    \"action\": \"Window.SetRemoteMode\"\n  },","new_string":"","replace_all":false},{"old_string":"  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"remote\",\n    \"action\": \"Window.SetLocalMode\"\n  },","new_string":"","replace_all":false}],"originalFileContents":"[\n  { \"keys\": \"(?P<keys>\\\\d*([\\\\$\\\\(\\\\)\\\\+\\\\-\\\\^\\\\{\\\\}0bBeEGhHjklLMwW]|g[eEg]|z[jk]))\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let position = text.movePosition(text.caretPosition, captures.keys);\n\n      text.gotoPosition(position);\n    }\"\n  },\n  { \"keys\": \"\\\\.\",\n    \"modes\": \"command, local\",\n    \"action\": \"Window.RepeatLastCommand\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*),\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let command = text.getRegister('Key::VimInlineFinder') || '';\n      const transfer = { f: 'F', F: 'f', t: 'T', T: 't' };\n\n      if (command.length == 2 && command.charAt(0) in transfer) {\n        command = transfer[command.charAt(0)] + command.charAt(1);\n        let position = text.movePosition(text.caretPosition, captures.count + command);\n\n        text.gotoPosition(position);\n      }\n    }\"\n  },\n  { \"keys\": \"/\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.Find\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*);\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let command = text.getRegister('Key::VimInlineFinder') || '';\n\n      if (command.length == 2 && /^[ft]/i.test(command)) {\n        let position = text.movePosition(text.caretPosition, captures.count + command);\n\n        text.gotoPosition(position);\n      }\n    }\"\n  },\n  { \"keys\": \"\\\\?\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.Find\"\n  },\n  { \"keys\": \"(?P<keys>\\\\d*(?P<key>[fFtT].))\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let position = text.movePosition(text.caretPosition, captures.keys);\n\n      text.gotoPosition(position);\n      text.setRegister('Key::VimInlineFinder', captures.key);\n    }\"\n  },\n  { \"keys\": \"i\",\n    \"modes\": \"command\",\n    \"action\": \"Window.SetNormalMode\"\n  },\n  { \"keys\": \"i\",\n    \"modes\": \"local\",\n    \"action\": \"Window.SetRemoteMode\"\n  },\n  { \"keys\": \"(\\\"(?P<named>[0-9a-zA-Z\\\\+]))?(?P<key>[pP])\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n\n      if (captures.key == 'p') {\n        text.keyMode = King.RemoteMode;\n      }\n\n      if (captures.named == '+') {\n        text.paste();\n      } else if (captures.named == '' || /[0-9a-z]/i.test(captures.named)) {\n        let string = text.getRegister(captures.named == '' ? '\\\"' : captures.named);\n\n        text.write(string);\n      }\n\n      if (captures.key == 'P') {\n        text.write(' ')\n      }\n    }\"\n  },\n  { \"keys\": \"(\\\"(?P<named>[0-9a-zA-Z\\\\+]))?y(?P<keys>y|0|\\\\d*([\\\\$\\\\(\\\\)\\\\+\\\\-\\\\^\\\\{\\\\}bBeEGhHjklLMwW]|g[eEg]|z[jk]|[fFtT].))\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let selectionText;\n\n      if (captures.keys == 'y') {\n        selectionText = text.selectionText();\n      } else {\n        let start = text.caretPosition;\n        let end = text.movePosition(text.caretPosition, captures.keys);\n\n        if (captures.keys == 'G') {\n          end = text.lineEnd(text.lineFromPosition(end));\n        } else if (/\\\\d*[ft]/.test(captures.keys)) {\n          end = text.movePositionOutsideChar(end + 1, 1);\n        }\n\n        if (start != -1 && end != -1) {\n          selectionText = text.subString(start, end);\n        }\n      }\n\n      if (selectionText) {\n        if (captures.named == '+') {\n          application.copy(selectionText);\n          text.setRegister('0', selectionText);\n        } else if (/[0-9a-z]/i.test(captures.named)) {\n          if (/[A-Z]/.test(captures.named)) {\n            selectionText = text.getRegister(captures.named.toLowerCase()).concat(selectionText);\n          }\n          text.setRegister(captures.named.toLowerCase(), selectionText);\n        }\n        text.setRegister('\\\"', selectionText);\n      }\n    }\"\n  },\n  { \"keys\": \"(?P<line>\\\\d*)z(?P<type>[btz])\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let caretLine = text.caretLine;\n      let line = Math.max(0, Number(captures.line) ? (Number(captures.line) - 1) : text.caretLine);\n      let topLine = text.visibleLineFromLine(line);\n\n      if (captures.type == 'b') {\n        topLine -= text.screenLineCapacity;\n      } else if (captures.type == 'z') {\n        topLine -= Math.floor(text.screenLineCapacity / 2);\n      }\n      text.scrollTop = Math.max(0, topLine);\n\n      if (line != caretLine) {\n        let position = text.lineStart(line);\n\n        text.setSelection(position);\n      }\n    }\"\n  },\n  { \"keys\": \"(?P<type>[vV])((?P<zero>0)|(?P<count>\\\\d*)((?P<key>[\\\\$\\\\(\\\\)\\\\+\\\\-\\\\^\\\\{\\\\}bBeEGhHjklLMwW]|g[eEg]|z[jk]|[fFtT].)|(?P<home><Home>)|(?P<end><End>)|(?P<h><Left>)|(?P<j><Down>)|(?P<k><Up>)|(?P<l><Right>)))\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      captures.key = ['h', 'j', 'k', 'l'].find(key => captures[key] != '') || captures.key;\n\n      if (captures.key == '') {\n        if (captures['home'] || captures['zero']) {\n          captures.key = '0';\n        } else if (captures['end']) {\n          captures.key = '$';\n        }\n      }\n      let text = window.activeText;\n      let position = text.movePosition(text.selectionCaretP(), captures.count + captures.key);\n\n      if (position != -1) {\n        text.setSelection(text.selectionAnchorP(), position);\n        text.setSelectionType(captures.type == 'V' ? King.ColumnSelection : King.StreamSelection);\n      }\n    }\"\n  },\n  { \"keys\": \"za\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.ToggleFold\"\n  },\n  { \"keys\": \"zc\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.Fold\"\n  },\n  { \"keys\": \"zC\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.FoldInside\"\n  },\n  { \"keys\": \"z[mM]\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.FoldAll\"\n  },\n  { \"keys\": \"zo\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.Unfold\"\n  },\n  { \"keys\": \"zO\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.UnfoldInside\"\n  },\n  { \"keys\": \"z[rR]\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.UnfoldAll\"\n  },\n  { \"keys\": \"<Backspace>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteBack\"\n  },\n  { \"keys\": \"<Backspace>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.Cdup\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Backspace>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToPreviousChar\"\n  },\n  { \"keys\": \"<Del>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteChar\"\n  },\n  { \"keys\": \"<Del>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.Remove\"\n  },  \n  { \"keys\": \"<Del>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.session.tree')\",\n    \"action\": \"SessionPane.Remove\"\n  },\n  { \"keys\": \"<Del>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.transfer.tree')\",\n    \"action\": \"TransferPane.Remove\"\n  },\n  { \"keys\": \"<Down>\",\n    \"modes\": \"Normal\",\n    \"action\": \"Text.MoveToNextLine\"\n  },\n  { \"keys\": \"<Down>\",\n    \"modes\": \"Remote\",\n    \"when\": \"window.activeText.isAutoCompleteVisible()\",\n    \"action\": \"Text.MoveToNextLine\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Down>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToNextLine\"\n  },\n  { \"keys\": \"<End>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToLineEnd\"\n  },\n  { \"keys\": \"<Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.Open\"\n  },\n  { \"keys\": \"<Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.Execute\"\n  },\n  { \"keys\": \"<Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.session.tree')\",\n    \"action\": \"SessionPane.Open\"\n  },\n  { \"keys\": \"<Enter>\",\n    \"modes\": \"remote\",\n    \"when\": \"window.isScope('terminal.view.text') && terminal.activeView().sessionState == King.Unconnected\",\n    \"action\": \"Terminal.ReconnectSession\"\n  },\n  { \"keys\": \"<Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.transfer.tree')\",\n    \"action\": \"TransferPane.Open\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Enter>\",\n    \"modes\": \"command, local\",\n    \"script\": \"(captures) => {\n      let text = window.activeText;\n      let caretLine = text.caretLine;\n\n      if (caretLine < text.lineCount - 1) {\n        let position = text.lineHome(Math.min(caretLine + (Number(captures.count) || 1), text.lineCount - 1));\n        text.gotoPosition(position);\n      }\n    }\"\n  },\n  { \"keys\": \"<Esc>\",\n    \"modes\": \"normal\",\n    \"action\": \"Window.SetCommandMode\"\n  },  \n  { \"keys\": \"<Esc>\",\n    \"modes\": \"normal, local\",\n    \"when\": \"window.activeText.isSelectionEmpty() == false\",\n    \"script\": \"() => { window.activeText.cancelSelection() }\"\n  },\n  { \"keys\": \"<Esc>\",\n    \"modes\": \"normal, local\",\n    \"when\": \"window.activeText.selectionCount() > 1\",\n    \"script\": \"() => { window.activeText.cancelMultipleSelections() }\"\n  },\n  { \"keys\": \"<Esc>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.activeText.isSnippetMode()\",\n    \"script\": \"() => { window.activeText.cancelSnippetMode() }\"\n  },\n  { \"keys\": \"<Esc>\",\n    \"when\": \"window.isScope('.view') && window.activeKit.isPaneVisible('search')\",\n    \"script\": \"() => { window.activeKit.hidePane('search') }\"\n  },\n  { \"keys\": \"<Esc>\",\n    \"modes\": \"remote\",\n    \"when\": \"window.activeText.isFreeTypeMode()\",\n    \"script\": \"() => { window.activeText.cancelFreeTypeMode() }\"\n  },\n  { \"keys\": \"<Esc>\",\n    \"modes\": \"normal, remote\",\n    \"when\": \"window.activeText.isAutoCompleteVisible()\",\n    \"script\": \"() => { window.activeText.cancelAutoComplete() }\"\n  },\n  { \"keys\": \"<F2>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.Rename\"\n  },\n  { \"keys\": \"<F3>\",\n    \"modes\": \"normal, local, widget\",\n    \"action\": \"Text.FindNext\"\n  },\n  { \"keys\": \"<F3>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree') && window.activeKit.pane('filer').localSystemMode == false\",\n    \"action\": \"FilerPane.Download\"\n  },\n  { \"keys\": \"<F3>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree') && window.activeKit.pane('filer').localSystemMode\",\n    \"action\": \"FilerPane.CopyTo\"\n  },\n  { \"keys\": \"<F4>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree') && window.activeKit.pane('filer').localSystemMode == false\",\n    \"action\": \"FilerPane.Upload\"\n  },\n  { \"keys\": \"<F4>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree') && window.activeKit.pane('filer').localSystemMode\",\n    \"action\": \"FilerPane.CopyFrom\"\n  },\n  { \"keys\": \"<F5>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.Refresh\"\n  },\n  { \"keys\": \"<F6>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MoveTo\"\n  },\n  { \"keys\": \"<F7>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopyDirectoryPathToTerminal\"\n  },\n  { \"keys\": \"<F8>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.SyncTerminalFolder\"\n  },\n  { \"keys\": \"<F9>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.AutoSyncTerminalFolder\"\n  },\n  { \"keys\": \"<F9>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.SortLinesCaseInsensitive\"\n  },\n  { \"keys\": \"<F11>\",\n    \"modes\": \"normal, command, widget\",\n    \"action\": \"Window.ToggleFullScreen\"\n  },\n  { \"keys\": \"<Home>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToLineHome\"\n  },\n  { \"keys\": \"<Ins>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.ToggleOvertype\"\n  },\n  { \"keys\": \"<Left>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MoveToPreviousChar\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Left>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToPreviousChar\"\n  },\n  { \"keys\": \"<PgDown>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MoveToNextPage\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<PgDown>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToNextPage\"\n  },\n  { \"keys\": \"<PgUp>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MoveToPreviousPage\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<PgUp>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToPreviousPage\"\n  },\n  { \"keys\": \"<Return>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.activeText.isReadOnly() == false\",\n    \"action\": \"Text.NewLine\"\n  },\n  { \"keys\": \"<Return>\",\n    \"modes\": \"normal, remote\",\n    \"when\": \"window.activeText.isAutoCompleteVisible() && window.activeText.isAutoCompletePreselected()\",\n    \"script\": \"() => { window.activeText.commitAutoComplete() }\"\n  },\n  { \"keys\": \"<Right>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MoveToNextChar\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Right>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToNextChar\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Space>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToNextChar\"\n  },\n  { \"keys\": \"<Tab>\",\n    \"modes\": \"normal\",\n    \"when\": \"() => {\n      let text = window.activeText;\n      let count = text.selectionCount();\n      for (let i = 0; i < count; i++) {\n        if (text.lineFromPosition(text.selectionEndP(i)) > text.lineFromPosition(text.selectionStartP(i)))\n          return true;\n      }\n      return false;\n    }\",\n    \"action\": \"Text.IncreaseLineIndent\"\n  },\n  { \"keys\": \"<Tab>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.activeText.canExpandSnippet()\",\n    \"script\": \"() => { window.activeText.expandSnippet() }\"\n  },\n  { \"keys\": \"<Tab>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.activeText.isAutoCompleteVisible()\",\n    \"script\": \"() => { window.activeText.commitAutoComplete() }\"\n  },\n  { \"keys\": \"<Tab>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.activeText.hasNextSnippetStop()\",\n    \"script\": \"() => { let text = window.activeText; text.cancelAutoComplete(); text.jumpToNextSnippetStop() }\"\n  },\n  { \"keys\": \"<Up>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MoveToPreviousLine\"\n  },\n  { \"keys\": \"<Up>\",\n    \"modes\": \"remote\",\n    \"when\": \"window.activeText.isAutoCompleteVisible()\",\n    \"action\": \"Text.MoveToPreviousLine\"\n  },\n  { \"keys\": \"(?P<count>\\\\d*)<Up>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToPreviousLine\"\n  },\n  { \"keys\": \"<Alt+'>\",\n    \"action\": \"Window.GotoSymbol\"\n  },\n  { \"keys\": \"<Alt+,>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToPreviousParagraph\"\n  },\n  { \"keys\": \"<Alt+->\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SplitPaneDown\"\n  },\n  { \"keys\": \"<Alt+->\",\n    \"modes\": \"normal, command, widget\",\n    \"when\": \"window.isScope('terminal.sender.*')\",\n    \"action\": \"SenderPane.RemoveSender\"\n  },\n  { \"keys\": \"<Alt+.>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToNextParagraph\"\n  },\n  { \"keys\": \"<Alt+/>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousWordPart\"\n  },\n  { \"keys\": \"<Alt+;>\",\n    \"action\": \"Window.ShowPaletteCommand\"\n  },\n  { \"keys\": \"<Alt+=>\",\n    \"modes\": \"normal, command, widget\",\n    \"when\": \"window.isScope('terminal.sender.*')\",\n    \"action\": \"SenderPane.AddSender\"\n  },\n  { \"keys\": \"<Alt+[>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPairStart\"\n  },\n  { \"keys\": \"<Alt+[>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SelectWindowPrevious\"\n  },\n  { \"keys\": \"<Alt+\\\\>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextWordPart\"\n  },\n  { \"keys\": \"<Alt+\\\\>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SplitPaneRight\"\n  },\n  { \"keys\": \"<Alt+]>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPairEnd\"\n  },\n  { \"keys\": \"<Alt+]>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SelectWindowNext\"\n  },\n  { \"keys\": \"<Alt+0>\",\n    \"script\": \"() => { window.activeKit.activateView(0) }\"\n  },\n  { \"keys\": \"<Alt+1>\",\n    \"script\": \"() => { window.activeKit.activateView(1) }\"\n  },\n  { \"keys\": \"<Alt+2>\",\n    \"script\": \"() => { window.activeKit.activateView(2) }\"\n  },\n  { \"keys\": \"<Alt+3>\",\n    \"script\": \"() => { window.activeKit.activateView(3) }\"\n  },\n  { \"keys\": \"<Alt+4>\",\n    \"script\": \"() => { window.activeKit.activateView(4) }\"\n  },\n  { \"keys\": \"<Alt+5>\",\n    \"script\": \"() => { window.activeKit.activateView(5) }\"\n  },\n  { \"keys\": \"<Alt+6>\",\n    \"action\": \"Window.ActivateEditor\"\n  },\n  { \"keys\": \"<Alt+7>\",\n    \"action\": \"Window.ActivateTerminal\"\n  },\n  { \"keys\": \"<Alt+B>\",\n    \"action\": \"Window.ShowPaletteMultiplexer\"\n  },\n  { \"keys\": \"<Alt+D>\",\n    \"when\": \"window.isScope('-terminal.filer.view.tree')\",\n    \"action\": \"Window.GotoAddressBar\"\n  },\n  { \"keys\": \"<Alt+D>\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.GotoAddressBar\"\n  },\n  { \"keys\": \"<Alt+Down>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SelectPaneBelow\"\n  },\n  { \"keys\": \"<Alt+End>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToLineEnd\"\n  },\n  { \"keys\": \"<Alt+Enter>\",\n    \"modes\": \"remote, local, widget\",\n    \"action\": \"Window.ToggleFocusMode\"\n  },\n  { \"keys\": \"<Alt+Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.Property\"\n  },\n  { \"keys\": \"<Alt+Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.session.tree')\",\n    \"action\": \"SessionPane.Property\"\n  },\n  { \"keys\": \"<Alt+Enter>\",\n    \"modes\": \"normal, command, widget\",\n    \"when\": \"window.isScope('terminal.sender.*')\",\n    \"action\": \"SenderPane.StartSender\"\n  },\n  { \"keys\": \"<Alt+F3>\",\n    \"modes\": \"remote, widget\",\n    \"action\": \"Text.FindNext\"\n  },\n  { \"keys\": \"<Alt+F4>\",\n    \"action\": \"Application.Exit\"\n  },\n  { \"keys\": \"<Alt+G>\",\n    \"modes\": \"remote, local, widget\",\n    \"action\": \"Window.GotoLine\"\n  },\n  { \"keys\": \"<Alt+H>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToPreviousChar\"\n  },\n  { \"keys\": \"<Alt+Home>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToLineHome\"\n  },\n  { \"keys\": \"<Alt+J>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToNextLine\"\n  },\n  { \"keys\": \"<Alt+K>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToPreviousLine\"\n  },\n  { \"keys\": \"<Alt+L>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToNextChar\"\n  },\n  { \"keys\": \"<Alt+Left>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SelectPaneLeft\"\n  },\n  { \"keys\": \"<Alt+Left>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.GoBackward\"\n  },\n  { \"keys\": \"<Alt+M><Alt+C>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.ToggleColumnEdit\"\n  },\n  { \"keys\": \"<Alt+M><Alt+H>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleHexEdit\"\n  },\n  { \"keys\": \"<Alt+M><Alt+I>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.ToggleMultipleCaret\"\n  },\n  { \"keys\": \"<Alt+M><Alt+R>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.ToggleReadOnly\"\n  },\n  { \"keys\": \"<Alt+M><Alt+S>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.ToggleSyncEdit\"\n  },\n  { \"keys\": \"<Alt+M><Alt+V>\",\n    \"modes\": \"normal, command\",\n    \"action\": \"Text.ToggleVirtualSpace\"\n  },\n  { \"keys\": \"<Alt+M><Alt+W>\",\n    \"modes\": \"normal, command, remote, local\",\n    \"action\": \"Text.ToggleWordWrap\"\n  },\n  { \"keys\": \"<Alt+N>\",\n    \"modes\": \"remote, widget\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.NewSession\"\n  },\n  { \"keys\": \"<Alt+N><Alt+C>\",\n    \"modes\": \"remote, local\",\n    \"action\": \"Text.MoveToNextCommand\"\n  },\n  { \"keys\": \"<Alt+N><Alt+E>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextWordEnd\"\n  },\n  { \"keys\": \"<Alt+N><Alt+F>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextFold\"\n  },\n  { \"keys\": \"<Alt+N><Alt+I>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextIndentation\"\n  },\n  { \"keys\": \"<Alt+N><Alt+P>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextParagraph\"\n  },\n  { \"keys\": \"<Alt+N><Alt+S>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextScope\"\n  },\n  { \"keys\": \"<Alt+N><Alt+]>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToNextPair\"\n  },\n  { \"keys\": \"<Alt+O>\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Window.GotoFile\"\n  },\n  { \"keys\": \"<Alt+O>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.OpenSession\"\n  },\n  { \"keys\": \"<Alt+O>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Window.GotoSession\"\n  },\n  { \"keys\": \"<Alt+P><Alt+C>\",\n    \"modes\": \"remote, local\",\n    \"action\": \"Text.MoveToPreviousCommand\"\n  },\n  { \"keys\": \"<Alt+P><Alt+E>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousWordEnd\"\n  },\n  { \"keys\": \"<Alt+P><Alt+F>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousFold\"\n  },\n  { \"keys\": \"<Alt+P><Alt+I>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousIndentation\"\n  },\n  { \"keys\": \"<Alt+P><Alt+P>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousParagraph\"\n  },\n  { \"keys\": \"<Alt+P><Alt+S>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousScope\"\n  },\n  { \"keys\": \"<Alt+P><Alt+[>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.MoveToPreviousPair\"\n  },\n  { \"keys\": \"<Alt+PgDown>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToNextStutteredPage\"\n  },\n  { \"keys\": \"<Alt+PgUp>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToPreviousStutteredPage\"\n  },\n  { \"keys\": \"<Alt+Q>\",\n    \"modes\": \"normal, command, widget\",\n    \"when\": \"window.isScope('terminal.sender.*')\",\n    \"action\": \"SenderPane.StopSender\"\n  },\n  { \"keys\": \"<Alt+R>\",\n    \"modes\": \"remote\",\n    \"when\": \"terminal.activeView().isMainScreen()\",\n    \"action\": \"Terminal.CompleteHistory\"\n  },\n  { \"keys\": \"<Alt+Right>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SelectPaneRight\"\n  },\n  { \"keys\": \"<Alt+Right>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.GoForward\"\n  },\n  { \"keys\": \"<Alt+S>\",\n    \"action\": \"Window.ShowPaletteAnything\"\n  },\n  { \"keys\": \"<Alt+T><Alt+B>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleMarginBlank\"\n  },\n  { \"keys\": \"<Alt+T><Alt+E>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleLineEnding\"\n  },\n  { \"keys\": \"<Alt+T><Alt+F>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleMarginFold\"\n  },\n  { \"keys\": \"<Alt+T><Alt+I>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleIndentGuide\"\n  },\n  { \"keys\": \"<Alt+T><Alt+L>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleColumnMarker\"\n  },\n  { \"keys\": \"<Alt+T><Alt+N>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleMarginNumber\"\n  },\n  { \"keys\": \"<Alt+T><Alt+S>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleMarginSymbol\"\n  },\n  { \"keys\": \"<Alt+T><Alt+T>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.ToggleMarginTimestamp\"\n  },\n  { \"keys\": \"<Alt+T><Alt+X>\",\n    \"modes\": \"normal, command\",\n    \"action\": \"Text.ToggleMarginText\"\n  },\n  { \"keys\": \"<Alt+T><Alt+W>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ToggleWrapSymbol\"\n  },\n  { \"keys\": \"<Alt+Up>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SelectPaneAbove\"\n  },\n  { \"keys\": \"<Alt+W><Alt+C>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.TogglePaneShell\"\n  },\n  { \"keys\": \"<Alt+W><Alt+D>\",\n    \"when\": \"window.activeDock != null\",\n    \"action\": \"Window.CloseActiveDock\"\n  },\n  { \"keys\": \"<Alt+W><Alt+E>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Window.TogglePaneExplorer\"\n  },\n  { \"keys\": \"<Alt+W><Alt+F>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.TogglePaneFiler\"\n  },\n  { \"keys\": \"<Alt+W><Alt+G>\",\n    \"when\": \"window.isScope('*.view')\",\n    \"action\": \"Window.CloseActiveGroup\"\n  },\n  { \"keys\": \"<Alt+W><Alt+H>\",\n    \"when\": \"window.isScope('*.view')\",\n    \"action\": \"Window.SplitViewHorizontally\"\n  },\n  { \"keys\": \"<Alt+W><Alt+L>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.TogglePaneOutline\"\n  },\n  { \"keys\": \"<Alt+W><Alt+M>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.ToggleMenuBar\"\n  },\n  { \"keys\": \"<Alt+W><Alt+O>\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Window.TogglePaneOutput\"\n  },\n  { \"keys\": \"<Alt+W><Alt+P>\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Window.TogglePaneProject\"\n  },\n  { \"keys\": \"<Alt+W><Alt+Q>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Window.ToggleQuickBar\"\n  },\n  { \"keys\": \"<Alt+W><Alt+S>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Window.TogglePaneSession\"\n  },\n  { \"keys\": \"<Alt+W><Alt+T>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Window.TogglePaneTransfer\"\n  },\n  { \"keys\": \"<Alt+W><Alt+U>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.ToggleStatusBar\"\n  },\n  { \"keys\": \"<Alt+W><Alt+V>\",\n    \"when\": \"window.isScope('*.view')\",\n    \"action\": \"Window.SplitViewVertically\"\n  },\n  { \"keys\": \"<Alt+W><Alt+X>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Window.TogglePaneSender\"\n  },\n  { \"keys\": \"<Alt+X>\",\n    \"modes\": \"normal, command, widget\",\n    \"when\": \"window.isScope('terminal.sender.*')\",\n    \"action\": \"SenderPane.ClearSender\"\n  },\n  { \"keys\": \"<Alt+X>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"script\": \"() => {\n       window.activeMuxDesktop.killMuxPane();\n     }\"\n  },\n  { \"keys\": \"<Alt+Z>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.TogglePaneZoom\"\n  },\n  { \"keys\": \"<Alt+Shift+?>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousWordPart\"\n  },\n  { \"keys\": \"<Alt+Shift+F3>\",\n    \"modes\": \"remote, widget\",\n    \"action\": \"Text.FindPrevious\"\n  },\n  { \"keys\": \"<Alt+Shift+Down>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToNextLine\"\n  },\n  { \"keys\": \"<Alt+Shift+Left>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToPreviousChar\"\n  },\n  { \"keys\": \"<Alt+Shift+N><Alt+Shift+E>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToNextWordEnd\"\n  },\n  { \"keys\": \"<Alt+Shift+N><Alt+Shift+F>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToNextFold\"\n  },\n  { \"keys\": \"<Alt+Shift+N><Alt+Shift+I>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToNextIndentation\"\n  },\n  { \"keys\": \"<Alt+Shift+N><Alt+Shift+P>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToNextParagraph\"\n  },\n  { \"keys\": \"<Alt+Shift+N><Alt+Shift+S>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToNextScope\"\n  },\n  { \"keys\": \"<Alt+Shift+N><Alt+Shift+}>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToNextPair\"\n  },\n  { \"keys\": \"<Alt+Shift+P><Alt+Shift+E>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousWordEnd\"\n  },\n  { \"keys\": \"<Alt+Shift+P><Alt+Shift+F>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousFold\"\n  },\n  { \"keys\": \"<Alt+Shift+P><Alt+Shift+I>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousIndentation\"\n  },\n  { \"keys\": \"<Alt+Shift+P><Alt+Shift+P>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousParagraph\"\n  },\n  { \"keys\": \"<Alt+Shift+P><Alt+Shift+S>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousScope\"\n  },\n  { \"keys\": \"<Alt+Shift+P><Alt+Shift+{>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPreviousPair\"\n  },\n  { \"keys\": \"<Alt+Shift+Right>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToNextChar\"\n  },\n  { \"keys\": \"<Alt+Shift+Up>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectColumnToPreviousLine\"\n  },\n  { \"keys\": \"<Alt+Shift+{>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPairStart\"\n  },\n  { \"keys\": \"<Alt+Shift+}>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Text.SelectToPairEnd\"\n  },\n  { \"keys\": \"<Alt+Shift+_>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SplitPaneUp\"\n  },\n  { \"keys\": \"<Alt+Shift+|>\",\n    \"when\": \"window.isScope('MuxGroup')\",\n    \"action\": \"Mux.SplitPaneLeft\"\n  },\n  { \"keys\": \"<Ctrl+.>\",\n    \"modes\": \"local\",\n    \"action\": \"Terminal.MoveToNextCommand\"\n  },\n  { \"keys\": \"<Ctrl+->\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ZoomOut\"\n  },\n  { \"keys\": \"<Ctrl+,>\",\n    \"modes\": \"local\",\n    \"action\": \"Terminal.MoveToPreviousCommand\"\n  },\n  { \"keys\": \"<Ctrl+/>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Comment\"\n  },\n  { \"keys\": \"<Ctrl+=>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ZoomIn\"\n  },\n  { \"keys\": \"<Ctrl+A>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectAll\"\n  },\n  { \"keys\": \"<Ctrl+B>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Tool.BuildScheme\"\n  },\n  { \"keys\": \"<Ctrl+B>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToPreviousPage\"\n  },\n  { \"keys\": \"<Ctrl+Backspace>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteWordToStart\"\n  },\n  { \"keys\": \"<Ctrl+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.Copy\"\n  },\n  { \"keys\": \"<Ctrl+C>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Ctrl+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedNames\"\n  },\n  { \"keys\": \"<Ctrl+C>\",\n    \"modes\": \"remote\",\n    \"when\": \"terminal.activeView().isModemTransmitting()\",\n    \"action\": \"Terminal.CancelModem\"\n  },\n  { \"keys\": \"<Ctrl+D>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Duplicate\"\n  },\n  { \"keys\": \"<Ctrl+D>\",\n    \"modes\": \"remote, local\",\n    \"when\": \"terminal.activeView().canWrite() == false\",\n    \"action\": \"Window.CloseActiveView\"\n  },\n  { \"keys\": \"<Ctrl+D>\",\n    \"modes\": \"command, local\",\n    \"script\": \"() => {\n      let text = window.activeText;\n      let halfScreenLineCount = Math.floor(text.screenLineCapacity / 2);\n      let caretLine = text.visibleLineFromPosition(text.caretPosition);\n      let line = Math.min(text.visibleLineCount, caretLine + halfScreenLineCount);\n      let position = text.visibleLineStart(line);\n\n      text.scrollTop = Math.min(text.visibleLineCount, text.topLine + halfScreenLineCount);\n      text.gotoPosition(position);\n    }\"\n  },\n  { \"keys\": \"<Ctrl+D>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.session.tree')\",\n    \"action\": \"SessionPane.Duplicate\"\n  },\n  { \"keys\": \"<Ctrl+Del>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteWordToEnd\"\n  },\n  { \"keys\": \"<Ctrl+Down>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ScrollLineDown\"\n  },\n  { \"keys\": \"<Ctrl+E>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.ScrollLineDown\"\n  },\n  { \"keys\": \"<Ctrl+End>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToDocumentEnd\"\n  },\n  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"normal\",\n    \"action\": \"Window.SetCommandMode\"\n  },\n  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"command\",\n    \"action\": \"Window.SetNormalMode\"\n  },\n  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"local\",\n    \"action\": \"Window.SetRemoteMode\"\n  },\n  { \"keys\": \"<Ctrl+Enter>\",\n    \"modes\": \"remote\",\n    \"action\": \"Window.SetLocalMode\"\n  },\n  { \"keys\": \"<Ctrl+F>\",\n    \"modes\": \"normal, local, widget\",\n    \"action\": \"Text.Find\"\n  },\n  { \"keys\": \"<Ctrl+F>\",\n    \"modes\": \"command\",\n    \"action\": \"Text.MoveToNextPage\"\n  },\n  { \"keys\": \"<Ctrl+F3>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.FindNextSelection\"\n  },\n  { \"keys\": \"<Ctrl+F9>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.SortLinesCaseSensitive\"\n  },\n  { \"keys\": \"<Ctrl+F11>\",\n    \"modes\": \"normal, command, widget\",\n    \"action\": \"Window.ToggleFullScreen\"\n  },\n  { \"keys\": \"<Ctrl+G>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.GotoSymbol\"\n  },\n  { \"keys\": \"<Ctrl+G>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Window.GotoLine\"\n  },\n  { \"keys\": \"<Ctrl+H>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Text.Replace\"\n  },\n  { \"keys\": \"<Ctrl+Home>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToDocumentStart\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+C>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SwitchStreamColumn\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+D>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.DropSelection\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+I>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.InvertSelection\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+L>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.LockSelection\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+N>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ActivateNextSelection\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+P>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ActivatePreviousSelection\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+S>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SplitSelection\"\n  },\n  { \"keys\": \"<Ctrl+I><Ctrl+X>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ExchangeCaretAnchor\"\n  },\n  { \"keys\": \"<Ctrl+Ins>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+A>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TrimLeadingBlanks\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+F>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.FormatSelection\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+K>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteWord\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+L>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteLine\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+T>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Tabify\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+U>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Untabify\"\n  },\n  { \"keys\": \"<Ctrl+K><Ctrl+Z>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TrimTrailingBlanks\"\n  },\n  { \"keys\": \"<Ctrl+L>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MakeLink\"\n  },\n  { \"keys\": \"<Ctrl+L>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.NewLink\"\n  },\n  { \"keys\": \"<Ctrl+L>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.view.text')\",\n    \"action\": \"Terminal.ClearScreen\"\n  },\n  { \"keys\": \"<Ctrl+M>\",\n    \"modes\": \"normal, command, local, remote, widget\",\n    \"action\": \"Window.ToggleFocusMode\"\n  },\n  { \"keys\": \"<Ctrl+Left>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToPreviousWordStart\"\n  },\n  { \"keys\": \"<Ctrl+N>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.NewFile\"\n  },\n  { \"keys\": \"<Ctrl+N>\",\n    \"modes\": \"command, local\",\n    \"map\": \"<Down>\"\n  },\n  { \"keys\": \"<Ctrl+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MakeFile\"\n  },\n  { \"keys\": \"<Ctrl+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.NewFile\"\n  },\n  { \"keys\": \"<Ctrl+O>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.OpenFile\"\n  },\n  { \"keys\": \"<Ctrl+P>\",\n    \"modes\": \"command, local\",\n    \"map\": \"<Up>\"\n  },  \n  { \"keys\": \"<Ctrl+P>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedNamesToTerminal\"\n  },\n  { \"keys\": \"<Ctrl+P>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.PreviewSymbol\"\n  },\n  { \"keys\": \"<Ctrl+Pause>\",\n    \"when\": \"window.isScope('editor.output.*')\",\n    \"action\": \"OutputPane.StopTool\"\n  },\n  { \"keys\": \"<Ctrl+Pause>\",\n    \"when\": \"window.isScope('-editor.output.*')\",\n    \"action\": \"Tool.StopToolDialog\"\n  },\n  { \"keys\": \"<Ctrl+Tab>\",\n    \"modes\": \"normal, local, remote\",\n    \"action\": \"Window.ActivateNextView\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Tab>\",\n    \"modes\": \"normal, local, remote\",\n    \"action\": \"Window.ActivatePreviousView\"\n  },\n  { \"keys\": \"<Ctrl+Right>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToNextWordStart\"\n  },\n  { \"keys\": \"<Ctrl+Q>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Window.Close\"\n  },\n  { \"keys\": \"<Ctrl+S>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.Save\"\n  },\n  { \"keys\": \"<Ctrl+S>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('.text, -terminal.view.text')\",\n    \"action\": \"Text.Save\"\n  },\n  { \"keys\": \"<Ctrl+T>\",\n    \"modes\": \"normal, command, local, remote, widget\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.NewSession\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+A>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.ConvertSpaceToTab\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+I>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeInverseCase\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+K>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageOneKeys\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+L>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeLowerCase\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+M>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.UnifyEolToCR\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+S>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.ConvertTabToSpace\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+T>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeTitleCase\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+T>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageTriggers\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+U>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeUpperCase\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+U>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageTunnels\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+W>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.UnifyEolToCRLF\"\n  },\n  { \"keys\": \"<Ctrl+T><Ctrl+X>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.UnifyEolToLF\"\n  },\n  { \"keys\": \"<Ctrl+U>\",\n    \"modes\": \"command, local\",\n    \"script\": \"() => {\n      let text = window.activeText;\n      let halfScreenLineCount = Math.floor(text.screenLineCapacity / 2);\n      let caretLine = text.visibleLineFromPosition(text.caretPosition);\n      let line = Math.max(0, caretLine - halfScreenLineCount);\n      let position = text.visibleLineStart(line);\n\n      text.scrollTop = Math.min(0, text.topLine - halfScreenLineCount);\n      text.gotoPosition(position);\n    }\"\n  },\n  { \"keys\": \"<Ctrl+Up>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ScrollLineUp\"\n  },\n  { \"keys\": \"<Ctrl+V>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.Paste\"\n  },\n  { \"keys\": \"<Ctrl+W>\",\n    \"modes\": \"normal, command, local, remote, widget\",\n    \"action\": \"Window.CloseActiveView\"\n  },\n  { \"keys\": \"<Ctrl+F4>\",\n    \"modes\": \"normal, command, local, remote, widget\",\n    \"action\": \"Window.CloseActiveView\"\n  },\n  { \"keys\": \"<Ctrl+X>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Cut\"\n  },\n  { \"keys\": \"<Ctrl+X><Ctrl+X>\",\n    \"modes\": \"remote\",\n    \"when\": \"terminal.activeView().isModemTransmitting()\",\n    \"action\": \"Terminal.CancelModem\"\n  },\n  { \"keys\": \"<Ctrl+Y>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Redo\"\n  },\n  { \"keys\": \"<Ctrl+Y>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.ScrollLineUp\"\n  },\n  { \"keys\": \"<Ctrl+Z>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Undo\"\n  },\n  { \"keys\": \"<Ctrl+Alt+,>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposePreviousWord\"\n  },\n  { \"keys\": \"<Ctrl+Alt+->\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.Fold\"\n  },\n  { \"keys\": \"<Ctrl+Alt+.>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposeNextWord\"\n  },\n  { \"keys\": \"<Ctrl+Alt+0>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.UnfoldInside\"\n  },\n  { \"keys\": \"<Ctrl+Alt+9>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.FoldInside\"\n  },\n  { \"keys\": \"<Ctrl+Alt+=>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.Unfold\"\n  },\n  { \"keys\": \"<Ctrl+Alt+Down>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposeNextLine\"\n  },\n  { \"keys\": \"<Ctrl+Alt+F3>\",\n    \"modes\": \"remote\",\n    \"action\": \"Text.FindNextSelection\"\n  },\n  { \"keys\": \"<Ctrl+Alt+L>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.LockScreen\"\n  },\n  { \"keys\": \"<Ctrl+Alt+Left>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposePreviousChar\"\n  },\n  { \"keys\": \"<Ctrl+Alt+Right>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposeNextChar\"\n  },\n  { \"keys\": \"<Ctrl+Alt+S>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.SaveAll\"\n  },\n  { \"keys\": \"<Ctrl+Alt+Up>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposePreviousLine\"\n  },\n  { \"keys\": \"<Ctrl+Alt+[>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.FoldAll\"\n  },\n  { \"keys\": \"<Ctrl+Alt+]>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.UnfoldAll\"\n  },\n  { \"keys\": \"<Ctrl+Shift+_>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectFold\"\n  },\n  { \"keys\": \"<Ctrl+Shift+.>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectScope\"\n  },\n  { \"keys\": \"<Ctrl+Shift+/>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Uncomment\"\n  },\n  { \"keys\": \"<Ctrl+Shift+/>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Terminal.SelectCommand\"\n  },\n  { \"keys\": \"<Ctrl+Shift+=>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectIndentation\"\n  },\n  { \"keys\": \"<Ctrl+Shift+A>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Terminal.SelectAll\"\n  },\n  { \"keys\": \"<Ctrl+Shift+A>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.SelectAll\"\n  },\n  { \"keys\": \"<Ctrl+Shift+B>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Tool.CancelBuildScheme\"\n  },\n  { \"keys\": \"<Ctrl+Shift+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedPaths\"\n  },\n  { \"keys\": \"<Ctrl+Shift+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.CopyOutput\"\n  },\n  { \"keys\": \"<Ctrl+Shift+C>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Ctrl+Shift+D>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.DuplicateSession\"\n  },\n  { \"keys\": \"<Ctrl+Shift+D>\",\n    \"modes\": \"normal, command\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Window.DuplicateView\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Down>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { window.activeText.scrollTop += 1 }\"\n  },\n  { \"keys\": \"<Ctrl+Shift+End>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToDocumentEnd\"\n  },\n  { \"keys\": \"<Ctrl+Shift+End>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { let text = window.activeText; text.scrollTop = text.visibleLineCount }\"\n  },\n  { \"keys\": \"<Ctrl+Shift+F>\",\n    \"modes\": \"local, remote, widget\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Text.Find\"\n  },\n  { \"keys\": \"<Ctrl+Shift+F>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Text.FindInFiles\"\n  },\n  { \"keys\": \"<Ctrl+Shift+F3>\",\n    \"modes\": \"normal, local, remote\",\n    \"action\": \"Text.FindPreviousSelection\"\n  },\n  { \"keys\": \"<Ctrl+Shift+H>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Text.ReplaceInFiles\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Home>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToDocumentStart\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Home>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { window.activeText.scrollTop = 0 }\"\n  },\n  { \"keys\": \"<Ctrl+Shift+K>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectWord\"\n  },\n  { \"keys\": \"<Ctrl+Shift+L>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectLine\"\n  },\n  { \"keys\": \"<Ctrl+Shift+L>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.view.text')\",\n    \"action\": \"Terminal.ClearScrollback\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Left>\",\n    \"modes\": \"normal, command\",\n    \"action\": \"Text.SelectToPreviousWordStart\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Left>\",\n    \"modes\": \"remote, local\",\n    \"action\": \"Terminal.MoveToPreviousCommand\"\n  },\n  { \"keys\": \"<Ctrl+Shift+M>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.SyncInput\"\n  },\n  { \"keys\": \"<Ctrl+Shift+N>\",\n    \"action\": \"Application.NewWindow\"\n  },\n  { \"keys\": \"<Ctrl+Shift+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MakeDirectory\"\n  },\n  { \"keys\": \"<Ctrl+Shift+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.NewDirectory\"\n  },\n  { \"keys\": \"<Ctrl+Shift+P>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectParagraph\"\n  },\n  { \"keys\": \"<Ctrl+Shift+P>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedPathsToTerminal\"\n  },\n  { \"keys\": \"<Ctrl+Shift+PgDown>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { let text = window.activeText; text.scrollTop += text.screenLineCapacity }\"\n  },\n  { \"keys\": \"<Ctrl+Shift+PgUp>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { let text = window.activeText; text.scrollTop -= text.screenLineCapacity }\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Q>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Window.Close\"\n  },\n  { \"keys\": \"<Ctrl+Shift+R>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Tool.RunToolDialog\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Right>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToNextWordStart\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Right>\",\n    \"modes\": \"remote, local\",\n    \"action\": \"Terminal.MoveToNextCommand\"\n  },\n  { \"keys\": \"<Ctrl+Shift+S>\",\n    \"modes\": \"normal, command\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.SaveAs\"\n  },\n  { \"keys\": \"<Ctrl+Shift+S>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.Save\"\n  },\n  { \"keys\": \"<Ctrl+Shift+T>\",\n    \"action\": \"Window.ReopenClosedView\"\n  },\n  { \"keys\": \"<Ctrl+Shift+Up>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { window.activeText.scrollTop -= 1 }\"\n  },\n  { \"keys\": \"<Ctrl+Shift+V>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.Paste\"\n  },\n  { \"keys\": \"<Ctrl+Shift+X>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"terminal.activeView().isFreeTypeMode()\",\n    \"action\": \"Terminal.Cut\"\n  },\n  { \"keys\": \"<Ctrl+Shift+X>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"terminal.activeView().isFreeTypeMode() == false\",\n    \"action\": \"Terminal.PasteSelectedText\"\n  },\n  { \"keys\": \"<Ctrl+Shift+[>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectPair\"\n  },\n  { \"keys\": \"<Ctrl+Shift+]>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectPair\"\n  },\n  { \"keys\": \"<Meta+.>\",\n    \"modes\": \"local\",\n    \"action\": \"Terminal.MoveToNextCommand\"\n  },\n  { \"keys\": \"<Meta+->\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ZoomOut\"\n  },\n  { \"keys\": \"<Meta+,>\",\n    \"modes\": \"local\",\n    \"action\": \"Terminal.MoveToPreviousCommand\"\n  },\n  { \"keys\": \"<Meta+/>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Comment\"\n  },\n  { \"keys\": \"<Meta+=>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.ZoomIn\"\n  },\n  { \"keys\": \"<Meta+0>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.UnfoldInside\"\n  },\n  { \"keys\": \"<Meta+0>\",\n    \"script\": \"() => { window.activeKit.activateView(0) }\"\n  },\n  { \"keys\": \"<Meta+1>\",\n    \"script\": \"() => { window.activeKit.activateView(1) }\"\n  },\n  { \"keys\": \"<Meta+2>\",\n    \"script\": \"() => { window.activeKit.activateView(2) }\"\n  },\n  { \"keys\": \"<Meta+3>\",\n    \"script\": \"() => { window.activeKit.activateView(3) }\"\n  },\n  { \"keys\": \"<Meta+4>\",\n    \"script\": \"() => { window.activeKit.activateView(4) }\"\n  },\n  { \"keys\": \"<Meta+5>\",\n    \"script\": \"() => { window.activeKit.activateView(5) }\"\n  },\n  { \"keys\": \"<Meta+6>\",\n    \"action\": \"Window.ActivateEditor\"\n  },\n  { \"keys\": \"<Meta+7>\",\n    \"action\": \"Window.ActivateTerminal\"\n  },\n  { \"keys\": \"<Meta+9>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.FoldInside\"\n  },\n  { \"keys\": \"<Meta+A>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.SelectAll\"\n  },\n  { \"keys\": \"<Meta+B>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Tool.BuildScheme\"\n  },\n  { \"keys\": \"<Meta+B>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.MoveToPreviousPage\"\n  },\n  { \"keys\": \"<Meta+Backspace>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteWordToStart\"\n  },\n  { \"keys\": \"<Meta+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.Copy\"\n  },\n  { \"keys\": \"<Meta+C>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"when\": \"window.isScope('.text')\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Meta+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedNames\"\n  },\n  { \"keys\": \"<Meta+C>\",\n    \"modes\": \"remote\",\n    \"when\": \"terminal.activeView().isModemTransmitting()\",\n    \"action\": \"Terminal.CancelModem\"\n  },\n  { \"keys\": \"<Meta+D>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Duplicate\"\n  },\n  { \"keys\": \"<Meta+D>\",\n    \"modes\": \"command, local\",\n    \"script\": \"() => {\n      let text = window.activeText;\n      let halfScreenLineCount = Math.floor(text.screenLineCapacity / 2);\n      let caretLine = text.visibleLineFromPosition(text.caretPosition);\n      let line = Math.min(text.visibleLineCount, caretLine + halfScreenLineCount);\n      let position = text.visibleLineStart(line);\n\n      text.scrollTop = Math.min(text.visibleLineCount, text.topLine + halfScreenLineCount);\n      text.gotoPosition(position);\n    }\"\n  },\n  { \"keys\": \"<Meta+D>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.session.tree')\",\n    \"action\": \"SessionPane.Duplicate\"\n  },\n  { \"keys\": \"<Meta+Del>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteWordToEnd\"\n  },\n  { \"keys\": \"<Meta+Down>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ScrollLineDown\"\n  },\n  { \"keys\": \"<Meta+E>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.ScrollLineDown\"\n  },\n  { \"keys\": \"<Meta+End>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToDocumentEnd\"\n  },\n  { \"keys\": \"<Meta+F>\",\n    \"modes\": \"normal, command, local, remote, widget\",\n    \"action\": \"Text.Find\"\n  },\n  { \"keys\": \"<Meta+F2>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Window.ToggleBookmark\"\n  },\n  { \"keys\": \"<Meta+F3>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.FindNextSelection\"\n  },\n  { \"keys\": \"<Meta+F9>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.SortLinesCaseSensitive\"\n  },\n  { \"keys\": \"<Meta+F11>\",\n    \"modes\": \"normal, command, widget\",\n    \"action\": \"Window.ToggleFullScreen\"\n  },\n  { \"keys\": \"<Meta+G>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.GotoSymbol\"\n  },\n  { \"keys\": \"<Meta+G>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Window.GotoLine\"\n  },\n  { \"keys\": \"<Meta+H>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Text.Replace\"\n  },\n  { \"keys\": \"<Meta+Home>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToDocumentStart\"\n  },\n  { \"keys\": \"<Meta+I><Meta+C>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SwitchStreamColumn\"\n  },\n  { \"keys\": \"<Meta+I><Meta+D>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.DropSelection\"\n  },\n  { \"keys\": \"<Meta+I><Meta+I>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.InvertSelection\"\n  },\n  { \"keys\": \"<Meta+I><Meta+L>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.LockSelection\"\n  },\n  { \"keys\": \"<Meta+I><Meta+N>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ActivateNextSelection\"\n  },\n  { \"keys\": \"<Meta+I><Meta+P>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ActivatePreviousSelection\"\n  },\n  { \"keys\": \"<Meta+I><Meta+S>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SplitSelection\"\n  },\n  { \"keys\": \"<Meta+I><Meta+X>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ExchangeCaretAnchor\"\n  },\n  { \"keys\": \"<Meta+Ins>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Meta+K><Meta+A>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TrimLeadingBlanks\"\n  },\n  { \"keys\": \"<Meta+K><Meta+F>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.FormatSelection\"\n  },\n  { \"keys\": \"<Meta+K><Meta+K>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteWord\"\n  },\n  { \"keys\": \"<Meta+K><Meta+L>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteLine\"\n  },\n  { \"keys\": \"<Meta+K><Meta+T>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Tabify\"\n  },\n  { \"keys\": \"<Meta+K><Meta+U>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Untabify\"\n  },\n  { \"keys\": \"<Meta+K><Meta+Z>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TrimTrailingBlanks\"\n  },\n  { \"keys\": \"<Meta+L>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MakeLink\"\n  },\n  { \"keys\": \"<Meta+L>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.NewLink\"\n  },\n  { \"keys\": \"<Meta+L>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.view.text')\",\n    \"action\": \"Terminal.ClearScreen\"\n  },\n  { \"keys\": \"<Meta+Left>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToPreviousWordStart\"\n  },\n  { \"keys\": \"<Meta+N>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.NewFile\"\n  },\n  { \"keys\": \"<Meta+N>\",\n    \"modes\": \"remote, widget\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.NewSession\"\n  },\n  { \"keys\": \"<Meta+N>\",\n    \"modes\": \"command, local\",\n    \"map\": \"<Down>\"\n  },\n  { \"keys\": \"<Meta+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MakeFile\"\n  },\n  { \"keys\": \"<Meta+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.NewFile\"\n  },\n  { \"keys\": \"<Meta+O>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.OpenFile\"\n  },\n  { \"keys\": \"<Meta+O>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.OpenSession\"\n  },\n  { \"keys\": \"<Meta+P>\",\n    \"modes\": \"command, local\",\n    \"map\": \"<Up>\"\n  },  \n  { \"keys\": \"<Meta+P>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedNamesToTerminal\"\n  },\n  { \"keys\": \"<Meta+P>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.PreviewSymbol\"\n  },\n  { \"keys\": \"<Meta+Pause>\",\n    \"when\": \"window.isScope('editor.output.*')\",\n    \"action\": \"OutputPane.StopTool\"\n  },\n  { \"keys\": \"<Meta+Pause>\",\n    \"when\": \"window.isScope('-editor.output.*')\",\n    \"action\": \"Tool.StopToolDialog\"\n  },\n  { \"keys\": \"<Meta+Right>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.MoveToNextWordStart\"\n  },\n  { \"keys\": \"<Meta+Q>\",\n    \"modes\": \"normal, command, local\",\n    \"action\": \"Window.Close\"\n  },\n  { \"keys\": \"<Meta+S>\",\n    \"modes\": \"normal, command\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.Save\"\n  },\n  { \"keys\": \"<Meta+S>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.Save\"\n  },\n  { \"keys\": \"<Meta+S>\",\n    \"modes\": \"normal, command\",\n    \"when\": \"window.isScope('.text, -terminal.view.text')\",\n    \"action\": \"Text.Save\"\n  },\n  { \"keys\": \"<Meta+T><Meta+A>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.ConvertSpaceToTab\"\n  },\n  { \"keys\": \"<Meta+T><Meta+I>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeInverseCase\"\n  },\n  { \"keys\": \"<Meta+T><Meta+K>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageOneKeys\"\n  },\n  { \"keys\": \"<Meta+T><Meta+L>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeLowerCase\"\n  },\n  { \"keys\": \"<Meta+T><Meta+M>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.UnifyEolToCR\"\n  },\n  { \"keys\": \"<Meta+T><Meta+S>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.ConvertTabToSpace\"\n  },\n  { \"keys\": \"<Meta+T><Meta+T>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeTitleCase\"\n  },\n  { \"keys\": \"<Meta+T><Meta+T>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageTriggers\"\n  },\n  { \"keys\": \"<Meta+T><Meta+U>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.MakeUpperCase\"\n  },\n  { \"keys\": \"<Meta+T><Meta+U>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageTunnels\"\n  },\n  { \"keys\": \"<Meta+T><Meta+W>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.UnifyEolToCRLF\"\n  },\n  { \"keys\": \"<Meta+T><Meta+X>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.UnifyEolToLF\"\n  },\n  { \"keys\": \"<Meta+U>\",\n    \"modes\": \"command, local\",\n    \"script\": \"() => {\n      let text = window.activeText;\n      let halfScreenLineCount = Math.floor(text.screenLineCapacity / 2);\n      let caretLine = text.visibleLineFromPosition(text.caretPosition);\n      let line = Math.max(0, caretLine - halfScreenLineCount);\n      let position = text.visibleLineStart(line);\n\n      text.scrollTop = Math.min(0, text.topLine - halfScreenLineCount);\n      text.gotoPosition(position);\n    }\"\n  },\n  { \"keys\": \"<Meta+Up>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.ScrollLineUp\"\n  },\n  { \"keys\": \"<Meta+V>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"when\": \"window.isScope('.text')\",\n    \"action\": \"Text.Paste\"\n  },\n  { \"keys\": \"<Meta+W>\",\n    \"modes\": \"normal, command, local, remote, widget\",\n    \"action\": \"Window.CloseActiveView\"\n  },\n  { \"keys\": \"<Meta+X>\",\n    \"modes\": \"normal, command\",\n    \"action\": \"Text.Cut\"\n  },\n  { \"keys\": \"<Meta+Y>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Redo\"\n  },\n  { \"keys\": \"<Meta+Y>\",\n    \"modes\": \"command, local\",\n    \"action\": \"Text.ScrollLineUp\"\n  },\n  { \"keys\": \"<Meta+Z>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Undo\"\n  },\n  { \"keys\": \"<Meta+Alt+,>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposePreviousWord\"\n  },\n  { \"keys\": \"<Meta+Alt+->\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.Fold\"\n  },\n  { \"keys\": \"<Meta+Alt+.>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposeNextWord\"\n  },\n  { \"keys\": \"<Meta+Alt+Down>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposeNextLine\"\n  },\n  { \"keys\": \"<Meta+Alt+L>\",\n    \"when\": \"window.isScope('editor.*, terminal.*')\",\n    \"action\": \"Window.LockScreen\"\n  },\n  { \"keys\": \"<Meta+Alt+Left>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposePreviousChar\"\n  },\n  { \"keys\": \"<Meta+Alt+Right>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposeNextChar\"\n  },\n  { \"keys\": \"<Meta+Alt+S>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.SaveAll\"\n  },\n  { \"keys\": \"<Meta+Alt+Up>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.TransposePreviousLine\"\n  },\n  { \"keys\": \"<Meta+Alt+[>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.FoldAll\"\n  },\n  { \"keys\": \"<Meta+Alt+]>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.UnfoldAll\"\n  },\n  { \"keys\": \"<Meta+Shift+_>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectFold\"\n  },\n  { \"keys\": \"<Meta+Shift+.>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectScope\"\n  },\n  { \"keys\": \"<Meta+Shift+/>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.Uncomment\"\n  },\n  { \"keys\": \"<Meta+Shift+=>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectIndentation\"\n  },\n  { \"keys\": \"<Meta+Shift+A>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Terminal.SelectAll\"\n  },\n  { \"keys\": \"<Meta+Shift+A>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.SelectAll\"\n  },\n  { \"keys\": \"<Meta+Shift+B>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Tool.CancelBuildScheme\"\n  },\n  { \"keys\": \"<Meta+Shift+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedPaths\"\n  },\n  { \"keys\": \"<Meta+Shift+C>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.CopyOutput\"\n  },\n  { \"keys\": \"<Meta+Shift+C>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Meta+Shift+Down>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { window.activeText.scrollTop += 1 }\"\n  },\n  { \"keys\": \"<Meta+Shift+End>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToDocumentEnd\"\n  },\n  { \"keys\": \"<Meta+Shift+End>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { let text = window.activeText; text.scrollTop = text.visibleLineCount }\"\n  },\n  { \"keys\": \"<Meta+Shift+F>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Text.FindInFiles\"\n  },\n  { \"keys\": \"<Meta+Shift+F3>\",\n    \"modes\": \"normal, local, remote\",\n    \"action\": \"Text.FindPreviousSelection\"\n  },\n  { \"keys\": \"<Meta+Shift+H>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Text.ReplaceInFiles\"\n  },\n  { \"keys\": \"<Meta+Shift+Home>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToDocumentStart\"\n  },\n  { \"keys\": \"<Meta+Shift+Home>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { window.activeText.scrollTop = 0 }\"\n  },\n  { \"keys\": \"<Meta+Shift+K>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectWord\"\n  },\n  { \"keys\": \"<Meta+Shift+L>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectLine\"\n  },\n  { \"keys\": \"<Meta+Shift+L>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.view.text')\",\n    \"action\": \"Terminal.ClearScrollback\"\n  },\n  { \"keys\": \"<Meta+Shift+M>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.SyncInput\"\n  },\n  { \"keys\": \"<Meta+Shift+N>\",\n    \"action\": \"Application.NewWindow\"\n  },\n  { \"keys\": \"<Meta+Shift+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.MakeDirectory\"\n  },\n  { \"keys\": \"<Meta+Shift+N>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.NewDirectory\"\n  },\n  { \"keys\": \"<Meta+Shift+Left>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToPreviousWordStart\"\n  },\n  { \"keys\": \"<Meta+Shift+Left>\",\n    \"modes\": \"remote, local\",\n    \"action\": \"Terminal.MoveToPreviousCommand\"\n  },\n  { \"keys\": \"<Meta+Shift+P>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectParagraph\"\n  },\n  { \"keys\": \"<Meta+Shift+P>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.filer.view.tree')\",\n    \"action\": \"FilerPane.CopySelectedPathsToTerminal\"\n  },\n  { \"keys\": \"<Meta+Shift+PgDown>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { let text = window.activeText; text.scrollTop += text.screenLineCapacity }\"\n  },\n  { \"keys\": \"<Meta+Shift+PgUp>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { let text = window.activeText; text.scrollTop -= text.screenLineCapacity }\"\n  },\n  { \"keys\": \"<Meta+Shift+Q>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Window.Close\"\n  },\n  { \"keys\": \"<Meta+Shift+R>\",\n    \"modes\": \"normal, widget\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Tool.RunToolDialog\"\n  },\n  { \"keys\": \"<Meta+Shift+Right>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToNextWordStart\"\n  },\n  { \"keys\": \"<Meta+Shift+Right>\",\n    \"modes\": \"remote, local\",\n    \"action\": \"Terminal.MoveToNextCommand\"\n  },\n  { \"keys\": \"<Meta+Shift+S>\",\n    \"modes\": \"normal, command\",\n    \"when\": \"window.isScope('editor.*')\",\n    \"action\": \"Editor.SaveAs\"\n  },\n  { \"keys\": \"<Meta+Shift+S>\",\n    \"modes\": \"local, remote\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.SaveSelectionAs\"\n  },\n  { \"keys\": \"<Meta+Shift+T>\",\n    \"action\": \"Window.ReopenClosedView\"\n  },\n  { \"keys\": \"<Meta+Shift+U>\",\n    \"when\": \"window.isScope('terminal.*')\",\n    \"action\": \"Terminal.ManageTunnels\"\n  },\n  { \"keys\": \"<Meta+Shift+Up>\",\n    \"modes\": \"remote\",\n    \"script\": \"() => { window.activeText.scrollTop -= 1 }\"\n  },\n  { \"keys\": \"<Meta+Shift+V>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.Paste\"\n  },\n  { \"keys\": \"<Meta+Shift+W>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Window.CloseActiveView\"\n  },\n  { \"keys\": \"<Meta+Shift+X>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Terminal.PasteSelectedText\"\n  },\n  { \"keys\": \"<Meta+Shift+Z>\",\n    \"modes\": \"normal, command\",\n    \"action\": \"Text.Redo\"\n  },\n  { \"keys\": \"<Meta+Shift+[>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectPair\"\n  },\n  { \"keys\": \"<Meta+Shift+]>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectPair\"\n  },\n  { \"keys\": \"<Shift+Backspace>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteLineToStart\"\n  },\n  { \"keys\": \"<Shift+Del>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DeleteLineToEnd\"\n  },\n  { \"keys\": \"<Shift+Del>\",\n    \"modes\": \"local, remote\",\n    \"action\": \"Text.Copy\"\n  },\n  { \"keys\": \"<Shift+Del>\",\n    \"modes\": \"remote\",\n    \"when\": \"window.activeText.isAutoCompleteVisible() && window.activeText.isAutoCompletePreselected()\",\n    \"script\": \"() => { window.activeText.discardAutoComplete() }\"\n  },\n  { \"keys\": \"<Shift+Down>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToNextLine\"\n  },\n  { \"keys\": \"<Shift+End>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToLineEnd\"\n  },\n  { \"keys\": \"<Shift+Enter>\",\n    \"modes\": \"widget\",\n    \"when\": \"window.isScope('terminal.outline.tree')\",\n    \"action\": \"OutlinePane.RepeatedlyExecute\"\n  },\n  { \"keys\": \"<Shift+F3>\",\n    \"modes\": \"normal, local, widget\",\n    \"action\": \"Text.FindPrevious\"\n  },\n  { \"keys\": \"<Shift+Home>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToLineHome\"\n  },\n  { \"keys\": \"<Shift+Ins>\",\n    \"modes\": \"normal, command, local, remote\",\n    \"action\": \"Text.Paste\"\n  },\n  { \"keys\": \"<Shift+Left>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToPreviousChar\"\n  },\n  { \"keys\": \"<Shift+PgDown>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToNextPage\"\n  },\n  { \"keys\": \"<Shift+PgUp>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToPreviousPage\"\n  },\n  { \"keys\": \"<Shift+Right>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToNextChar\"\n  },\n  { \"keys\": \"<Shift+Tab>\",\n    \"modes\": \"normal\",\n    \"action\": \"Text.DecreaseLineIndent\"\n  },\n  { \"keys\": \"<Shift+Tab>\",\n    \"modes\": \"normal\",\n    \"when\": \"window.activeText.hasPreviousSnippetStop()\",\n    \"script\": \"() => { let text = window.activeText; text.cancelAutoComplete(); text.jumpToPreviousSnippetStop() }\"\n  },\n  { \"keys\": \"<Shift+Up>\",\n    \"modes\": \"normal, local\",\n    \"action\": \"Text.SelectToPreviousLine\"\n  }\n]","structuredPatch":[{"oldStart":1071,"oldLines":22,"newStart":1071,"newLines":6,"lines":["     \"modes\": \"normal, local\",","     \"action\": \"Text.MoveToDocumentEnd\"","   },","-  { \"keys\": \"<Ctrl+Enter>\",","-    \"modes\": \"normal\",","-    \"action\": \"Window.SetCommandMode\"","-  },","-  { \"keys\": \"<Ctrl+Enter>\",","-    \"modes\": \"command\",","-    \"action\": \"Window.SetNormalMode\"","-  },","-  { \"keys\": \"<Ctrl+Enter>\",","-    \"modes\": \"local\",","-    \"action\": \"Window.SetRemoteMode\"","-  },","-  { \"keys\": \"<Ctrl+Enter>\",","-    \"modes\": \"remote\",","-    \"action\": \"Window.SetLocalMode\"","-  },","   { \"keys\": \"<Ctrl+F>\",","     \"modes\": \"normal, local, widget\",","     \"action\": \"Text.Find\""]}],"userModified":false}}
{"parentUuid":"dacb5e21-303b-434f-aa75-94357a52567a","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01SRZjf1gahQgWbWRMnpAr5i","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01WBB7cqaS6QV8JAJ86dMD3F","name":"TodoWrite","input":{"todos":[{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"completed","activeForm":"正在删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"in_progress","activeForm":"正在验证修改结果"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":2,"cache_creation_input_tokens":498,"cache_read_input_tokens":57717,"cache_creation":{"ephemeral_5m_input_tokens":498,"ephemeral_1h_input_tokens":0},"output_tokens":248,"service_tier":"standard"}},"requestId":"req_011CSbHW4p1Rf3omaMfDqZ2L","type":"assistant","uuid":"b76a32eb-a094-4709-b73a-30fb67dbe3d3","timestamp":"2025-08-29T03:24:06.171Z"}
{"parentUuid":"b76a32eb-a094-4709-b73a-30fb67dbe3d3","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01WBB7cqaS6QV8JAJ86dMD3F","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"e14b441b-807c-4cbd-9b14-104a93cecc80","timestamp":"2025-08-29T03:24:06.219Z","toolUseResult":{"oldTodos":[{"content":"备份当前的 wind.keymaps 文件","status":"in_progress","activeForm":"正在备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}],"newTodos":[{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"completed","activeForm":"正在删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"in_progress","activeForm":"正在验证修改结果"}]}}
{"parentUuid":"e14b441b-807c-4cbd-9b14-104a93cecc80","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_014F2ymBBWgqDEE7Ycs1Fp32","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"让我验证是否已成功删除所有 Ctrl+Enter 绑定："}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":1,"cache_creation_input_tokens":417,"cache_read_input_tokens":58215,"cache_creation":{"ephemeral_5m_input_tokens":417,"ephemeral_1h_input_tokens":0},"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CSbHWYUek4cfMfXhG3BG3","type":"assistant","uuid":"291ef679-29ac-4ba6-80a4-0fb37c6694dc","timestamp":"2025-08-29T03:24:10.959Z"}
{"parentUuid":"291ef679-29ac-4ba6-80a4-0fb37c6694dc","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_014F2ymBBWgqDEE7Ycs1Fp32","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01Q8KgMbobDwfHbfkddhc2sp","name":"Grep","input":{"pattern":"<Ctrl\\+Enter>","glob":"wind.keymaps","output_mode":"count"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":1,"cache_creation_input_tokens":417,"cache_read_input_tokens":58215,"cache_creation":{"ephemeral_5m_input_tokens":417,"ephemeral_1h_input_tokens":0},"output_tokens":120,"service_tier":"standard"}},"requestId":"req_011CSbHWYUek4cfMfXhG3BG3","type":"assistant","uuid":"5a97f465-0e67-4463-90bd-95dcaf2c557e","timestamp":"2025-08-29T03:24:11.951Z"}
{"parentUuid":"5a97f465-0e67-4463-90bd-95dcaf2c557e","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01Q8KgMbobDwfHbfkddhc2sp","type":"tool_result","content":"No matches found\n\nFound 0 total occurrences across 0 files."}]},"uuid":"7b5f41f9-9c6a-43c8-ba3f-6881685c9dbc","timestamp":"2025-08-29T03:24:12.017Z","toolUseResult":{"mode":"count","numFiles":0,"filenames":[],"content":"","numMatches":0}}
{"parentUuid":"7b5f41f9-9c6a-43c8-ba3f-6881685c9dbc","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01LEy1U88vqd4zaihhDbyQDE","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_0136QT5gr7R6H4oQzThM7kSW","name":"TodoWrite","input":{"todos":[{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"completed","activeForm":"正在删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"completed","activeForm":"正在验证修改结果"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":150,"cache_read_input_tokens":58632,"cache_creation":{"ephemeral_5m_input_tokens":150,"ephemeral_1h_input_tokens":0},"output_tokens":246,"service_tier":"standard"}},"requestId":"req_011CSbHWytcndZ37wvUZpV7p","type":"assistant","uuid":"b7408914-070b-4f71-99d6-bedd9bf7c8a3","timestamp":"2025-08-29T03:24:17.880Z"}
{"parentUuid":"b7408914-070b-4f71-99d6-bedd9bf7c8a3","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_0136QT5gr7R6H4oQzThM7kSW","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"1dd916d1-5e34-407e-b52d-3175bfb51249","timestamp":"2025-08-29T03:24:17.934Z","toolUseResult":{"oldTodos":[{"content":"备份当前的 wind.keymaps 文件","status":"in_progress","activeForm":"正在备份当前的 wind.keymaps 文件"},{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"pending","activeForm":"删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"pending","activeForm":"验证修改结果"}],"newTodos":[{"content":"删除所有 Ctrl+Enter 快捷键绑定","status":"completed","activeForm":"正在删除所有 Ctrl+Enter 快捷键绑定"},{"content":"验证修改结果","status":"completed","activeForm":"正在验证修改结果"}]}}
{"parentUuid":"1dd916d1-5e34-407e-b52d-3175bfb51249","isSidechain":false,"userType":"external","cwd":"E:\\WindTerm_2.7.0\\global","sessionId":"8df49d56-ab03-407d-98a6-828dc5c0b936","version":"1.0.96","gitBranch":"","message":{"id":"msg_01PMHmEdwgGb4j69iquTW3fv","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"完成！已成功从 WindTerm 配置中删除所有 Ctrl+Enter 快捷键绑定。现在 Ctrl+Enter 不再被 WindTerm 占用，可以用于其他应用程序或用途。\n\n修改后需要重启 WindTerm 才能生效。"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":0,"cache_creation_input_tokens":368,"cache_read_input_tokens":58782,"cache_creation":{"ephemeral_5m_input_tokens":368,"ephemeral_1h_input_tokens":0},"output_tokens":87,"service_tier":"standard"}},"requestId":"req_011CSbHXRG6aqzgBB6fADQcT","type":"assistant","uuid":"81faebda-b95b-4ca1-9893-c7af9c696b58","timestamp":"2025-08-29T03:24:23.789Z"}
