{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"E:\\","sessionId":"f27b793b-7ff0-41e2-a71f-30819810de39","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"123"},"uuid":"e05b0b90-f193-4124-94a3-25fe81b43153","timestamp":"2025-08-29T03:49:55.495Z"}
{"parentUuid":"e05b0b90-f193-4124-94a3-25fe81b43153","isSidechain":false,"userType":"external","cwd":"E:\\","sessionId":"f27b793b-7ff0-41e2-a71f-30819810de39","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":[{"type":"text","text":"[Request interrupted by user]"}]},"uuid":"1977a6ce-cb11-4e95-9adc-18728595cb37","timestamp":"2025-08-29T03:49:56.063Z"}
{"parentUuid":"1977a6ce-cb11-4e95-9adc-18728595cb37","isSidechain":false,"userType":"external","cwd":"E:\\","sessionId":"f27b793b-7ff0-41e2-a71f-30819810de39","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."},"isMeta":true,"uuid":"441e5ce4-d67d-47ce-8ffc-eb6d7594a466","timestamp":"2025-08-29T03:49:59.387Z"}
{"parentUuid":"441e5ce4-d67d-47ce-8ffc-eb6d7594a466","isSidechain":false,"userType":"external","cwd":"E:\\","sessionId":"f27b793b-7ff0-41e2-a71f-30819810de39","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"<command-name>/exit</command-name>\n          <command-message>exit</command-message>\n          <command-args></command-args>"},"uuid":"692f4998-43a8-4fff-a549-cfeed84f461a","timestamp":"2025-08-29T03:49:59.375Z"}
{"parentUuid":"692f4998-43a8-4fff-a549-cfeed84f461a","isSidechain":false,"userType":"external","cwd":"E:\\","sessionId":"f27b793b-7ff0-41e2-a71f-30819810de39","version":"1.0.96","gitBranch":"","type":"user","message":{"role":"user","content":"<local-command-stdout>(no content)</local-command-stdout>"},"uuid":"3cbcb669-c210-47ab-95a3-e16d5ef3b3c7","timestamp":"2025-08-29T03:49:59.375Z"}
